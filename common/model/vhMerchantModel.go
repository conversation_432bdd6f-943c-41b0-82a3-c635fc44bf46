package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhMerchantModel = (*customVhMerchantModel)(nil)

type (
	// VhMerchantModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhMerchantModel.
	VhMerchantModel interface {
		vhMerchantModel
	}

	customVhMerchantModel struct {
		*defaultVhMerchantModel
	}
)

// NewVhMerchantModel returns a model for the database table.
func NewVhMerchantModel(conn sqlx.SqlConn) VhMerchantModel {
	return &customVhMerchantModel{
		defaultVhMerchantModel: newVhMerchantModel(conn),
	}
}
