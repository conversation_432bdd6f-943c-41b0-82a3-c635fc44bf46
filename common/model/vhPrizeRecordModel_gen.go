// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhPrizeRecordFieldNames          = builder.RawFieldNames(&VhPrizeRecord{})
	vhPrizeRecordRows                = strings.Join(vhPrizeRecordFieldNames, ",")
	vhPrizeRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(vhPrizeRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhPrizeRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(vhPrizeRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhPrizeRecordModel interface {
		Insert(ctx context.Context, data *VhPrizeRecord) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhPrizeRecord) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhPrizeRecord, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhPrizeRecord, error)
		Update(ctx context.Context, data *VhPrizeRecord) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhPrizeRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhPrizeRecord struct {
		Id             uint64    `db:"id"`
		Uid            uint64    `db:"uid"`             // 用户id
		BatchId        int64     `db:"batch_id"`        // 批次id
		ShortCode      string    `db:"short_code"`      // 简码
		GoosName       string    `db:"goos_name"`       // 商品名称
		PrizeId        uint64    `db:"prize_id"`        // 奖品id
		PrizeName      string    `db:"prize_name"`      // 奖品名
		PrizeTime      time.Time `db:"prize_time"`      // 中奖时间
		Status         uint64    `db:"status"`          // 状态(1未核销，2已核销，3已过期)
		ExpireTime     time.Time `db:"expire_time"`     // 过期时间（奖品失效时间）
		Mid            uint64    `db:"mid"`             // 核销商家id，0表示未核销
		RedemptionTime time.Time `db:"redemption_time"` // 核销时间，1970-01-01表示未核销
	}
)

func newVhPrizeRecordModel(conn sqlx.SqlConn) *defaultVhPrizeRecordModel {
	return &defaultVhPrizeRecordModel{
		conn:  conn,
		table: "`vh_prize_record`",
	}
}

func (m *defaultVhPrizeRecordModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhPrizeRecordModel) FindOne(ctx context.Context, id uint64) (*VhPrizeRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhPrizeRecordRows, m.table)
	var resp VhPrizeRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeRecordModel) Insert(ctx context.Context, data *VhPrizeRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhPrizeRecordRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Uid, data.BatchId, data.ShortCode, data.GoosName, data.PrizeId, data.PrizeName, data.PrizeTime, data.Status, data.ExpireTime, data.Mid, data.RedemptionTime)
	return ret, err
}

func (m *defaultVhPrizeRecordModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhPrizeRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhPrizeRecordRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.Uid, data.BatchId, data.ShortCode, data.GoosName, data.PrizeId, data.PrizeName, data.PrizeTime, data.Status, data.ExpireTime, data.Mid, data.RedemptionTime)
	return ret, err
}

func (m *defaultVhPrizeRecordModel) Update(ctx context.Context, data *VhPrizeRecord) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhPrizeRecordRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Uid, data.BatchId, data.ShortCode, data.GoosName, data.PrizeId, data.PrizeName, data.PrizeTime, data.Status, data.ExpireTime, data.Mid, data.RedemptionTime, data.Id)
	return err
}

func (m *defaultVhPrizeRecordModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhPrizeRecordRows).From(m.table)
}

func (m *defaultVhPrizeRecordModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhPrizeRecord, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhPrizeRecord
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeRecordModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhPrizeRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhPrizeRecordModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhPrizeRecordModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeRecordModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeRecordModel) TableName() string {
	return m.table
}
