package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhMerchantRedemptionModel = (*customVhMerchantRedemptionModel)(nil)

type (
	// VhMerchantRedemptionModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhMerchantRedemptionModel.
	VhMerchantRedemptionModel interface {
		vhMerchantRedemptionModel
	}

	customVhMerchantRedemptionModel struct {
		*defaultVhMerchantRedemptionModel
	}
)

// NewVhMerchantRedemptionModel returns a model for the database table.
func NewVhMerchantRedemptionModel(conn sqlx.SqlConn) VhMerchantRedemptionModel {
	return &customVhMerchantRedemptionModel{
		defaultVhMerchantRedemptionModel: newVhMerchantRedemptionModel(conn),
	}
}
