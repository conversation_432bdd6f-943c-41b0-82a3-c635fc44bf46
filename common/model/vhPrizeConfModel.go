package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhPrizeConfModel = (*customVhPrizeConfModel)(nil)

type (
	// VhPrizeConfModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhPrizeConfModel.
	VhPrizeConfModel interface {
		vhPrizeConfModel
	}

	customVhPrizeConfModel struct {
		*defaultVhPrizeConfModel
	}
)

// NewVhPrizeConfModel returns a model for the database table.
func NewVhPrizeConfModel(conn sqlx.SqlConn) VhPrizeConfModel {
	return &customVhPrizeConfModel{
		defaultVhPrizeConfModel: newVhPrizeConfModel(conn),
	}
}
