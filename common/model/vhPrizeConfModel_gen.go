// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhPrizeConfFieldNames          = builder.RawFieldNames(&VhPrizeConf{})
	vhPrizeConfRows                = strings.Join(vhPrizeConfFieldNames, ",")
	vhPrizeConfRowsExpectAutoSet   = strings.Join(stringx.Remove(vhPrizeConfFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhPrizeConfRowsWithPlaceHolder = strings.Join(stringx.Remove(vhPrizeConfFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhPrizeConfModel interface {
		Insert(ctx context.Context, data *VhPrizeConf) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhPrizeConf) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhPrizeConf, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhPrizeConf, error)
		Update(ctx context.Context, data *VhPrizeConf) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhPrizeConfModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhPrizeConf struct {
		Id             uint64    `db:"id"`
		BatchId        uint64    `db:"batch_id"`        // 批次id
		RegionCode     string    `db:"region_code"`     // 大区code
		RegionName     string    `db:"region_name"`     // 大区名称
		PrizeName      string    `db:"prize_name"`      // 奖品名
		Status         uint64    `db:"status"`          // 状态1启用，2禁用
		Points         uint64    `db:"points"`          // 积分（单位分，中奖一次可以兑换多少分）
		RedeemQuantity uint64    `db:"redeem_quantity"` // 总中奖瓶数(单位瓶)
		PrizeCt        uint64    `db:"prize_ct"`        // 总中奖次数
		WinCt          uint64    `db:"win_ct"`          // 已中奖次数
		ValidDays      uint64    `db:"valid_days"`      // 有效天数(多少天后过期)
		CreateTime     time.Time `db:"create_time"`     // 创建时间
	}
)

func newVhPrizeConfModel(conn sqlx.SqlConn) *defaultVhPrizeConfModel {
	return &defaultVhPrizeConfModel{
		conn:  conn,
		table: "`vh_prize_conf`",
	}
}

func (m *defaultVhPrizeConfModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhPrizeConfModel) FindOne(ctx context.Context, id uint64) (*VhPrizeConf, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhPrizeConfRows, m.table)
	var resp VhPrizeConf
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeConfModel) Insert(ctx context.Context, data *VhPrizeConf) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhPrizeConfRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.BatchId, data.RegionCode, data.RegionName, data.PrizeName, data.Status, data.Points, data.RedeemQuantity, data.PrizeCt, data.WinCt, data.ValidDays)
	return ret, err
}

func (m *defaultVhPrizeConfModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhPrizeConf) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhPrizeConfRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.BatchId, data.RegionCode, data.RegionName, data.PrizeName, data.Status, data.Points, data.RedeemQuantity, data.PrizeCt, data.WinCt, data.ValidDays)
	return ret, err
}

func (m *defaultVhPrizeConfModel) Update(ctx context.Context, data *VhPrizeConf) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhPrizeConfRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.BatchId, data.RegionCode, data.RegionName, data.PrizeName, data.Status, data.Points, data.RedeemQuantity, data.PrizeCt, data.WinCt, data.ValidDays, data.Id)
	return err
}

func (m *defaultVhPrizeConfModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhPrizeConfRows).From(m.table)
}

func (m *defaultVhPrizeConfModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhPrizeConf, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhPrizeConf
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeConfModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhPrizeConfModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhPrizeConfModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhPrizeConfModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeConfModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhPrizeConfModel) TableName() string {
	return m.table
}
