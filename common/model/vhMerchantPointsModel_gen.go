// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhMerchantPointsFieldNames          = builder.RawFieldNames(&VhMerchantPoints{})
	vhMerchantPointsRows                = strings.Join(vhMerchantPointsFieldNames, ",")
	vhMerchantPointsRowsExpectAutoSet   = strings.Join(stringx.Remove(vhMerchantPointsFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhMerchantPointsRowsWithPlaceHolder = strings.Join(stringx.Remove(vhMerchantPointsFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhMerchantPointsModel interface {
		Insert(ctx context.Context, data *VhMerchantPoints) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhMerchantPoints) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhMerchantPoints, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhMerchantPoints, error)
		FindOneByMidShortCode(ctx context.Context, mid uint64, shortCode string) (*VhMerchantPoints, error)
		Update(ctx context.Context, data *VhMerchantPoints) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhMerchantPointsModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhMerchantPoints struct {
		Id            uint64 `db:"id"`
		Mid           uint64 `db:"mid"`            // 商家id
		ShortCode     string `db:"short_code"`     // 商品简码
		GoodsName     string `db:"goods_name"`     // 商品名
		TotalPoints   uint64 `db:"total_points"`   // 累计积分（单位分）
		CurrentPoints uint64 `db:"current_points"` // 当前积分（单位分）
	}
)

func newVhMerchantPointsModel(conn sqlx.SqlConn) *defaultVhMerchantPointsModel {
	return &defaultVhMerchantPointsModel{
		conn:  conn,
		table: "`vh_merchant_points`",
	}
}

func (m *defaultVhMerchantPointsModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhMerchantPointsModel) FindOne(ctx context.Context, id uint64) (*VhMerchantPoints, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhMerchantPointsRows, m.table)
	var resp VhMerchantPoints
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantPointsModel) FindOneByMidShortCode(ctx context.Context, mid uint64, shortCode string) (*VhMerchantPoints, error) {
	var resp VhMerchantPoints
	query := fmt.Sprintf("select %s from %s where `mid` = ? and `short_code` = ? limit 1", vhMerchantPointsRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, mid, shortCode)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantPointsModel) Insert(ctx context.Context, data *VhMerchantPoints) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, vhMerchantPointsRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Mid, data.ShortCode, data.GoodsName, data.TotalPoints, data.CurrentPoints)
	return ret, err
}

func (m *defaultVhMerchantPointsModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhMerchantPoints) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, vhMerchantPointsRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.Mid, data.ShortCode, data.GoodsName, data.TotalPoints, data.CurrentPoints)
	return ret, err
}

func (m *defaultVhMerchantPointsModel) Update(ctx context.Context, newData *VhMerchantPoints) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhMerchantPointsRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.Mid, newData.ShortCode, newData.GoodsName, newData.TotalPoints, newData.CurrentPoints, newData.Id)
	return err
}

func (m *defaultVhMerchantPointsModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhMerchantPointsRows).From(m.table)
}

func (m *defaultVhMerchantPointsModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhMerchantPoints, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhMerchantPoints
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantPointsModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhMerchantPointsModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhMerchantPointsModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhMerchantPointsModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantPointsModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantPointsModel) TableName() string {
	return m.table
}
