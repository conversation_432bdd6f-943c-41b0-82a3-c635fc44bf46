// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhMerchantFieldNames          = builder.RawFieldNames(&VhMerchant{})
	vhMerchantRows                = strings.Join(vhMerchantFieldNames, ",")
	vhMerchantRowsExpectAutoSet   = strings.Join(stringx.Remove(vhMerchantFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhMerchantRowsWithPlaceHolder = strings.Join(stringx.Remove(vhMerchantFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhMerchantModel interface {
		Insert(ctx context.Context, data *VhMerchant) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhMerchant) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhMerchant, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhMerchant, error)
		FindOneByPhone(ctx context.Context, phone string) (*VhMerchant, error)
		Update(ctx context.Context, data *VhMerchant) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhMerchantModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhMerchant struct {
		Id                   uint64    `db:"id"`
		OpenId               string    `db:"open_id"`                // 小程序open_id
		Phone                string    `db:"phone"`                  // 手机号
		CompanyName          string    `db:"company_name"`           // 公司名称
		ContactName          string    `db:"contact_name"`           // 联系人姓名
		UnifiedSocialCode    string    `db:"unified_social_code"`    // 社会统一代码
		BusinessLicenseImage string    `db:"business_license_image"` // 营业执照图片
		ProvinceId           uint64    `db:"province_id"`            // 省id
		CityId               uint64    `db:"city_id"`                // 市id
		DistrictId           uint64    `db:"district_id"`            // 区id
		ProvinceName         string    `db:"province_name"`          // 省名称
		CityName             string    `db:"city_name"`              // 市名称
		DistrictName         string    `db:"district_name"`          // 区名称
		Address              string    `db:"address"`                // 完整地址(包含省市区)
		CreateTime           time.Time `db:"create_time"`            // 创建时间
		Status               uint64    `db:"status"`                 // 1未提交，2审核中，3通过，4驳回（可以再提交）
		Reason               string    `db:"reason"`                 // 驳回原因
		LastLoginTime        time.Time `db:"last_login_time"`        // 最后登录时间
	}
)

func newVhMerchantModel(conn sqlx.SqlConn) *defaultVhMerchantModel {
	return &defaultVhMerchantModel{
		conn:  conn,
		table: "`vh_merchant`",
	}
}

func (m *defaultVhMerchantModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhMerchantModel) FindOne(ctx context.Context, id uint64) (*VhMerchant, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhMerchantRows, m.table)
	var resp VhMerchant
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantModel) FindOneByPhone(ctx context.Context, phone string) (*VhMerchant, error) {
	var resp VhMerchant
	query := fmt.Sprintf("select %s from %s where `phone` = ? limit 1", vhMerchantRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, phone)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantModel) Insert(ctx context.Context, data *VhMerchant) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhMerchantRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OpenId, data.Phone, data.CompanyName, data.ContactName, data.UnifiedSocialCode, data.BusinessLicenseImage, data.ProvinceId, data.CityId, data.DistrictId, data.ProvinceName, data.CityName, data.DistrictName, data.Address, data.Status, data.Reason, data.LastLoginTime)
	return ret, err
}

func (m *defaultVhMerchantModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhMerchant) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhMerchantRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.OpenId, data.Phone, data.CompanyName, data.ContactName, data.UnifiedSocialCode, data.BusinessLicenseImage, data.ProvinceId, data.CityId, data.DistrictId, data.ProvinceName, data.CityName, data.DistrictName, data.Address, data.Status, data.Reason, data.LastLoginTime)
	return ret, err
}

func (m *defaultVhMerchantModel) Update(ctx context.Context, newData *VhMerchant) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhMerchantRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.OpenId, newData.Phone, newData.CompanyName, newData.ContactName, newData.UnifiedSocialCode, newData.BusinessLicenseImage, newData.ProvinceId, newData.CityId, newData.DistrictId, newData.ProvinceName, newData.CityName, newData.DistrictName, newData.Address, newData.Status, newData.Reason, newData.LastLoginTime, newData.Id)
	return err
}

func (m *defaultVhMerchantModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhMerchantRows).From(m.table)
}

func (m *defaultVhMerchantModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhMerchant, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhMerchant
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhMerchantModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhMerchantModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhMerchantModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantModel) TableName() string {
	return m.table
}
