package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhBatchModel = (*customVhBatchModel)(nil)

type (
	// VhBatchModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhBatchModel.
	VhBatchModel interface {
		vhBatchModel
	}

	customVhBatchModel struct {
		*defaultVhBatchModel
	}
)

// NewVhBatchModel returns a model for the database table.
func NewVhBatchModel(conn sqlx.SqlConn) VhBatchModel {
	return &customVhBatchModel{
		defaultVhBatchModel: newVhBatchModel(conn),
	}
}
