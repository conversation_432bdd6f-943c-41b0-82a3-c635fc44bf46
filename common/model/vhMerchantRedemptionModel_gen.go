// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhMerchantRedemptionFieldNames          = builder.RawFieldNames(&VhMerchantRedemption{})
	vhMerchantRedemptionRows                = strings.Join(vhMerchantRedemptionFieldNames, ",")
	vhMerchantRedemptionRowsExpectAutoSet   = strings.Join(stringx.Remove(vhMerchantRedemptionFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhMerchantRedemptionRowsWithPlaceHolder = strings.Join(stringx.Remove(vhMerchantRedemptionFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhMerchantRedemptionModel interface {
		Insert(ctx context.Context, data *VhMerchantRedemption) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhMerchantRedemption) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhMerchantRedemption, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhMerchantRedemption, error)
		Update(ctx context.Context, data *VhMerchantRedemption) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhMerchantRedemptionModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhMerchantRedemption struct {
		Id             uint64    `db:"id"`
		Mid            uint64    `db:"mid"`             // 商家id
		ShortCode      string    `db:"short_code"`      // 商品简码
		GoodsName      string    `db:"goods_name"`      // 商品名
		RedeemQuantity uint64    `db:"redeem_quantity"` // 兑换瓶数
		ProvinceName   string    `db:"province_name"`   // 省名称
		CityName       string    `db:"city_name"`       // 市名称
		DistrictName   string    `db:"district_name"`   // 区名称
		Address        string    `db:"address"`         // 完整收货地址(包含省市区)
		Consignee      string    `db:"consignee"`       // 收货人
		ContactPhone   string    `db:"contact_phone"`   // 联系人电话
		CreateTime     time.Time `db:"create_time"`     // 申请时间
		Status         uint64    `db:"status"`          // 1待发货，2已发货，3已签收
		ShippingCode   string    `db:"shipping_code"`   // 物流单号
		ShipTime       time.Time `db:"ship_time"`       // 发货时间
	}
)

func newVhMerchantRedemptionModel(conn sqlx.SqlConn) *defaultVhMerchantRedemptionModel {
	return &defaultVhMerchantRedemptionModel{
		conn:  conn,
		table: "`vh_merchant_redemption`",
	}
}

func (m *defaultVhMerchantRedemptionModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhMerchantRedemptionModel) FindOne(ctx context.Context, id uint64) (*VhMerchantRedemption, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhMerchantRedemptionRows, m.table)
	var resp VhMerchantRedemption
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantRedemptionModel) Insert(ctx context.Context, data *VhMerchantRedemption) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhMerchantRedemptionRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Mid, data.ShortCode, data.GoodsName, data.RedeemQuantity, data.ProvinceName, data.CityName, data.DistrictName, data.Address, data.Consignee, data.ContactPhone, data.Status, data.ShippingCode, data.ShipTime)
	return ret, err
}

func (m *defaultVhMerchantRedemptionModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhMerchantRedemption) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhMerchantRedemptionRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.Mid, data.ShortCode, data.GoodsName, data.RedeemQuantity, data.ProvinceName, data.CityName, data.DistrictName, data.Address, data.Consignee, data.ContactPhone, data.Status, data.ShippingCode, data.ShipTime)
	return ret, err
}

func (m *defaultVhMerchantRedemptionModel) Update(ctx context.Context, data *VhMerchantRedemption) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhMerchantRedemptionRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Mid, data.ShortCode, data.GoodsName, data.RedeemQuantity, data.ProvinceName, data.CityName, data.DistrictName, data.Address, data.Consignee, data.ContactPhone, data.Status, data.ShippingCode, data.ShipTime, data.Id)
	return err
}

func (m *defaultVhMerchantRedemptionModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhMerchantRedemptionRows).From(m.table)
}

func (m *defaultVhMerchantRedemptionModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhMerchantRedemption, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhMerchantRedemption
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantRedemptionModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhMerchantRedemptionModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhMerchantRedemptionModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhMerchantRedemptionModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantRedemptionModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhMerchantRedemptionModel) TableName() string {
	return m.table
}
