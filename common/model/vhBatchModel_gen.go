// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhBatchFieldNames          = builder.RawFieldNames(&VhBatch{})
	vhBatchRows                = strings.Join(vhBatchFieldNames, ",")
	vhBatchRowsExpectAutoSet   = strings.Join(stringx.Remove(vhBatchFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhBatchRowsWithPlaceHolder = strings.Join(stringx.Remove(vhBatchFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhBatchModel interface {
		Insert(ctx context.Context, data *VhBatch) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhBatch) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhBatch, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhBatch, error)
		Update(ctx context.Context, data *VhBatch) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhBatchModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhBatch struct {
		Id         uint64    `db:"id"`
		Title      string    `db:"title"`       // 批次名称
		ShortCode  string    `db:"short_code"`  // 简码
		GoodsName  string    `db:"goods_name"`  // 产品名称
		Desc       string    `db:"desc"`        // 描述
		Status     uint64    `db:"status"`      // 1启用，2禁用
		CreateTime time.Time `db:"create_time"` // 创建时间
		StartTime  time.Time `db:"start_time"`  // 开始时间
		EndTime    time.Time `db:"end_time"`    // 结束时间
	}
)

func newVhBatchModel(conn sqlx.SqlConn) *defaultVhBatchModel {
	return &defaultVhBatchModel{
		conn:  conn,
		table: "`vh_batch`",
	}
}

func (m *defaultVhBatchModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhBatchModel) FindOne(ctx context.Context, id uint64) (*VhBatch, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhBatchRows, m.table)
	var resp VhBatch
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhBatchModel) Insert(ctx context.Context, data *VhBatch) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, vhBatchRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Title, data.ShortCode, data.GoodsName, data.Desc, data.Status, data.StartTime, data.EndTime)
	return ret, err
}

func (m *defaultVhBatchModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhBatch) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, vhBatchRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.Title, data.ShortCode, data.GoodsName, data.Desc, data.Status, data.StartTime, data.EndTime)
	return ret, err
}

func (m *defaultVhBatchModel) Update(ctx context.Context, data *VhBatch) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhBatchRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Title, data.ShortCode, data.GoodsName, data.Desc, data.Status, data.StartTime, data.EndTime, data.Id)
	return err
}

func (m *defaultVhBatchModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhBatchRows).From(m.table)
}

func (m *defaultVhBatchModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhBatch, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhBatch
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhBatchModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhBatchModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhBatchModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhBatchModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhBatchModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhBatchModel) TableName() string {
	return m.table
}
