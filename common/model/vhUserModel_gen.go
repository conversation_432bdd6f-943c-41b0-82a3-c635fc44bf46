// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhUserFieldNames          = builder.RawFieldNames(&VhUser{})
	vhUserRows                = strings.Join(vhUserFieldNames, ",")
	vhUserRowsExpectAutoSet   = strings.Join(stringx.Remove(vhUserFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhUserRowsWithPlaceHolder = strings.Join(stringx.Remove(vhUserFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhUserModel interface {
		Insert(ctx context.Context, data *VhUser) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhUser) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhUser, error)
		FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhUser, error)
		FindOneByPhone(ctx context.Context, phone string) (*VhUser, error)
		Update(ctx context.Context, data *VhUser) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhUserModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhUser struct {
		Id            uint64    `db:"id"`
		OpenId        string    `db:"open_id"`         // 小程序open_id
		Phone         string    `db:"phone"`           // 手机号
		CreateTime    time.Time `db:"create_time"`     // 创建时间
		LastLoginTime time.Time `db:"last_login_time"` // 最后登录时间
	}
)

func newVhUserModel(conn sqlx.SqlConn) *defaultVhUserModel {
	return &defaultVhUserModel{
		conn:  conn,
		table: "`vh_user`",
	}
}

func (m *defaultVhUserModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhUserModel) FindOne(ctx context.Context, id uint64) (*VhUser, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhUserRows, m.table)
	var resp VhUser
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhUserModel) FindOneByPhone(ctx context.Context, phone string) (*VhUser, error) {
	var resp VhUser
	query := fmt.Sprintf("select %s from %s where `phone` = ? limit 1", vhUserRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, phone)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhUserModel) Insert(ctx context.Context, data *VhUser) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, vhUserRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.OpenId, data.Phone, data.LastLoginTime)
	return ret, err
}

func (m *defaultVhUserModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhUser) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, vhUserRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.OpenId, data.Phone, data.LastLoginTime)
	return ret, err
}

func (m *defaultVhUserModel) Update(ctx context.Context, newData *VhUser) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhUserRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.OpenId, newData.Phone, newData.LastLoginTime, newData.Id)
	return err
}

func (m *defaultVhUserModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhUserRows).From(m.table)
}

func (m *defaultVhUserModel) FindOneByQuery(ctx context.Context, builder squirrel.SelectBuilder) (*VhUser, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	var resp VhUser
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhUserModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhUserModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhUserModel) FindCustom(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhUserModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhUserModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhUserModel) TableName() string {
	return m.table
}
