package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhPrizeRecordModel = (*customVhPrizeRecordModel)(nil)

type (
	// VhPrizeRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhPrizeRecordModel.
	VhPrizeRecordModel interface {
		vhPrizeRecordModel
	}

	customVhPrizeRecordModel struct {
		*defaultVhPrizeRecordModel
	}
)

// NewVhPrizeRecordModel returns a model for the database table.
func NewVhPrizeRecordModel(conn sqlx.SqlConn) VhPrizeRecordModel {
	return &customVhPrizeRecordModel{
		defaultVhPrizeRecordModel: newVhPrizeRecordModel(conn),
	}
}
