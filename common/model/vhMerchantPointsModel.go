package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhMerchantPointsModel = (*customVhMerchantPointsModel)(nil)

type (
	// VhMerchantPointsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhMerchantPointsModel.
	VhMerchantPointsModel interface {
		vhMerchantPointsModel
	}

	customVhMerchantPointsModel struct {
		*defaultVhMerchantPointsModel
	}
)

// NewVhMerchantPointsModel returns a model for the database table.
func NewVhMerchantPointsModel(conn sqlx.SqlConn) VhMerchantPointsModel {
	return &customVhMerchantPointsModel{
		defaultVhMerchantPointsModel: newVhMerchantPointsModel(conn),
	}
}
