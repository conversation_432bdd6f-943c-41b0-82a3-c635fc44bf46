package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhBatchRegionConfModel = (*customVhBatchRegionConfModel)(nil)

type (
	// VhBatchRegionConfModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhBatchRegionConfModel.
	VhBatchRegionConfModel interface {
		vhBatchRegionConfModel
	}

	customVhBatchRegionConfModel struct {
		*defaultVhBatchRegionConfModel
	}
)

// NewVhBatchRegionConfModel returns a model for the database table.
func NewVhBatchRegionConfModel(conn sqlx.SqlConn) VhBatchRegionConfModel {
	return &customVhBatchRegionConfModel{
		defaultVhBatchRegionConfModel: newVhBatchRegionConfModel(conn),
	}
}
