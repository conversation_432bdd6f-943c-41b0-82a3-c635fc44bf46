package model

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ VhUserModel = (*customVhUserModel)(nil)

type (
	// VhUserModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhUserModel.
	VhUserModel interface {
		vhUserModel
		ClearCache(ctx context.Context, user *VhUser) error
	}

	customVhUserModel struct {
		*defaultVhUserModel
	}
)

// NewVhUserModel returns a model for the database table.
func NewVhUserModel(conn sqlx.SqlConn, c cache.CacheConf) VhUserModel {
	return &customVhUserModel{
		defaultVhUserModel: newVhUserModel(conn, c),
	}
}

func (m *customVhUserModel) ClearCache(ctx context.Context, user *VhUser) error {
	vhMulandoVhUserIdKey := fmt.Sprintf("%s%v", cacheVhMulandoVhUserIdPrefix, user.Id)
	vhMulandoVhUserTelephoneKey := fmt.Sprintf("%s%v", cacheVhMulandoVhUserTelephonePrefix, user.Telephone)
	return m.DelCacheCtx(ctx, vhMulandoVhUserIdKey, vhMulandoVhUserTelephoneKey)
}
