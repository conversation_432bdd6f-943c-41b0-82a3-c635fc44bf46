package model

import (
	"context"
	"database/sql"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type (
	TransModel interface {
		Trans(ctx context.Context, fn func(ctx context.Context, tx *sql.Tx) error) error
	}

	defaultTransModel struct {
		conn sqlx.SqlConn
	}
)

func NewTransModel(conn sqlx.SqlConn) *defaultTransModel {
	return &defaultTransModel{
		conn,
	}
}

func (m *defaultTransModel) Trans(ctx context.Context, fn func(ctx context.Context, tx *sql.Tx) error) (err error) {
	db, err := m.conn.RawDB()
	if err != nil {
		return err
	}
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	err = fn(ctx, tx)
	if err != nil {
		return err
	}
	return nil
}
