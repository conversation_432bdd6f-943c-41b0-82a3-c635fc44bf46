package function

import (
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

// GenerateOrderNo 生成订单号（简单版本，不检查重复）
// prefix: 订单号前缀 (MLDM/MLDS)
// 注意：此函数不检查数据库重复，如需确保唯一性请使用带检查的版本
func GenerateOrderNo(prefix string) string {
	// 获取当前时间的年月日格式 (YYMMDD)
	now := time.Now()
	dateStr := now.Format("060102") // 250630

	// 生成6位随机数
	rand.Seed(time.Now().UnixNano())
	randomNum := rand.Intn(999999) // 0-999999
	randomStr := fmt.Sprintf("%06d", randomNum) // 补齐6位

	return fmt.Sprintf("%s%s%s", prefix, dateStr, randomStr)
}

// GenerateMainOrderNo 生成主订单号
func GenerateMainOrderNo() string {
	return GenerateOrderNo("MLDM")
}

// GenerateSubOrderNo 生成子订单号
func GenerateSubOrderNo() string {
	return GenerateOrderNo("MLDS")
}

// ParseItemsInfo 解析商品简码信息，兼容多种格式
// 支持格式：
// 1. 完整格式：简码1*数量1+简码2*数量2
// 2. 简化格式：简码1+简码2（数量默认为1）
// 3. 空值：返回空数组
func ParseItemsInfo(itemsInfoStr string) []map[string]interface{} {
	var itemsInfo []map[string]interface{}

	// 如果字段为空，返回空数组
	if itemsInfoStr == "" {
		return itemsInfo
	}

	// 按加号分割
	items := strings.Split(itemsInfoStr, "+")
	for _, item := range items {
		item = strings.TrimSpace(item) // 去除空格
		if item == "" {
			continue // 跳过空项
		}

		parts := strings.Split(item, "*")
		if len(parts) == 2 {
			// 完整格式：简码*数量
			num, err := strconv.Atoi(strings.TrimSpace(parts[1]))
			if err != nil {
				num = 1 // 数量解析失败时默认为1
			}
			itemsInfo = append(itemsInfo, map[string]interface{}{
				"short_code": strings.TrimSpace(parts[0]),
				"num":        num,
				"name":       "", // 非盲盒商品name为空字符串
			})
		} else if len(parts) == 1 {
			// 简化格式：只有简码，数量默认为1
			itemsInfo = append(itemsInfo, map[string]interface{}{
				"short_code": strings.TrimSpace(parts[0]),
				"num":        1, // 默认数量为1
				"name":       "", // 非盲盒商品name为空字符串
			})
		}
	}

	return itemsInfo
}
