package xerr

var message map[uint32]string

func init() {
	message = make(map[uint32]string)
	message[OK] = "SUCCESS"
	message[ServerCommonError] = "服务器开小差啦,稍后再来试一试"
	message[RequestParamError] = "参数错误"
	message[DbError] = "数据库繁忙,请稍后再试"
	message[DbUpdateAffectedZeroError] = "更新数据影响行数为0"
	message[DataNoExistError] = "数据不存在"
	message[UserNotExist] = "用户不存在"
	message[TokenExpireError] = "Token过期或无效"
	message[DataExist] = "数据已存在"
	message[Concurrent] = "操作太过频繁啦,稍后再来试一试"
	message[FullSpace] = "存储空间已满"
	message[TeamFullSpace] = "团队存储空间已满"
	message[KttNotExist] = "快团团未授权或已过期,请先授权店铺"
	message[TeamFullNumber] = "团队成员已满"
}

func MapErrMsg(errCode uint32) string {
	if msg, ok := message[errCode]; ok {
		return msg
	} else {
		return "服务器开小差啦,稍后再来试一试"
	}
}
