package xredis

import (
	"engine/common/xerr"
	"errors"
	"fmt"
	"github.com/gomodule/redigo/redis"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stringx"
	"time"
)

type XFun func(conn redis.Conn) (interface{}, error)

const (
	MaxLockTime = 2000
	ScriptLock  = `
	if redis.call("get", KEYS[1]) == ARGV[1] then
		-- 返回0，代表key不在
		return redis.call("del", KEYS[1])
	else
		-- key在，但是值不对 代表其他地方已经使用该key
		return 0
	end
`
)

var LockErr = errors.New("lock fail")

func Do(pool *redis.Pool, fun XFun) (interface{}, error) {
	c := pool.Get()
	defer func() {
		_ = c.Close()
	}()

	result, e := fun(c)
	if e != nil && e != redis.ErrNil {
		if _, ok := e.(*xerr.CodeError); !ok {
			logx.Error(fmt.Sprintf("XRedis Do error %s", e))
		}
	}

	return result, e
}

func Lock(pool *redis.Pool, key string, expire int, fun XFun) (interface{}, error) {
	return Do(pool, func(conn redis.Conn) (interface{}, error) {
		var isLock bool
		v := fmt.Sprintf("%d-%s", time.Now().Unix(), stringx.Randn(6))
		defer func() {
			//捕获全局异常
			if err := recover(); err != nil {
				logx.Error("XRedis lock error %s", err)
			}

			if isLock {
				//加锁成功后才允许解锁 如果锁已经被其他人使用 lua脚本里面会自动跳过
				lua := redis.NewScript(1, ScriptLock)
				_, e := redis.Int(lua.Do(conn, key, v))

				if e != nil {
					logx.Error("XRedis lock DEL key err, key: %s, value: %s, error: %s", key, v, e.Error())
				}
			}
		}()

		timer := time.NewTimer(time.Duration(expire) * time.Millisecond)
		_, err := redis.String(conn.Do("SET", key, v, "NX", "PX", expire))
		if err != nil {
			//加锁失败 直接退出
			logx.Error("XRedis lock set err %s", err.Error())
			return nil, LockErr
		}

		isLock = true

		//超时管理
		go func() {
			<-timer.C

			//超时后不用在释放锁
			isLock = false
		}()

		return fun(conn)
	})
}
