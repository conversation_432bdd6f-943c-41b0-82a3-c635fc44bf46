package xredis

const (
	// AuctionBid
	/*
		KEYS[1] bind_price ARGV[1] 获取时的价格
		KEYS[2] bind_price ARGV[1] 获取时的价格
	*/
	AuctionBid = `
	local k1 = redis.call("get",KEYS[1])
	local k2 = redis.call("get",KEYS[2])
	return {k1,k2}
	if redis.call("get", KEYS[1]) == ARGV[1] then
		-- 返回0，代表key不在
		redis.call("LPUSH", KEYS[2], ARGV[2])
		redis.call("LPUSH", KEYS[2], ARGV[2])
	else
		-- key在，但是值不对 代表其他地方已经使用该key
		return 0
	end
`
)
