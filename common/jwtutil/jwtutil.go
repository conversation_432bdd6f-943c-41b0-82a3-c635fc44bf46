package jwtutil

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

// JWTUtil 结构体包含 JWT 相关的配置
type JWTUtil struct {
	SecretKey  []byte
	Expiration time.Duration
}

// NewJWTUtil 创建一个新的 JWTUtil 实例
func NewJWTUtil(secretKey string, expiration time.Duration) *JWTUtil {
	return &JWTUtil{
		SecretKey:  []byte(secretKey),
		Expiration: expiration,
	}
}

// GenerateToken 生成JWT Token
func (jwtUtil *JWTUtil) GenerateToken(info map[string]interface{}) (string, error) {
	// 创建一个新的JWT令牌
	claims := jwt.MapClaims{
		"exp": time.Now().Add(jwtUtil.Expiration).Unix(),
	}

	// 将info中的键值对展开到claims中
	for key, value := range info {
		claims[key] = value
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 使用密钥进行签名
	tokenString, err := token.SignedString(jwtUtil.SecretKey)
	if err != nil {
		return "", fmt.Errorf("生成Token失败: %v", err)
	}

	return tokenString, nil
}

// VerifyToken 验证JWT Token
func (jwtUtil *JWTUtil) VerifyToken(tokenString string) (jwt.MapClaims, error) {
	// 解析JWT令牌
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法和密钥
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("未知的签名方法: %v", token.Header["alg"])
		}
		return jwtUtil.SecretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("%v", err)
	}

	// 验证令牌的有效性
	if !token.Valid {
		return nil, fmt.Errorf("无效的Token")
	}

	// 获取声明
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("无效的Token声明")
	}

	return claims, nil
}

// GetUser 获取用户信息
func (jwtUtil *JWTUtil) GetUser(ctx context.Context) jwt.MapClaims {
	if user, ok := ctx.Value("jwt_user").(jwt.MapClaims); ok {
		return user
	}
	return map[string]interface{}{}
}

// GetUid 获取uid
func (jwtUtil *JWTUtil) GetUid(ctx context.Context) int64 {
	if user, ok := ctx.Value("jwt_user").(jwt.MapClaims); ok {
		return cast.ToInt64(user["uid"])
	}
	return 0
}

// GetUserName 获取用户名称
func (jwtUtil *JWTUtil) GetUserName(ctx context.Context) string {
	if user, ok := ctx.Value("jwt_user").(jwt.MapClaims); ok {
		return cast.ToString(user["user_name"])
	}
	return ""
}
