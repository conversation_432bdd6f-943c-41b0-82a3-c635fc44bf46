package validation

import (
	"github.com/go-playground/validator/v10"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// registerCheckFunc  注册自定义验证规则
func (v *Verify) registerCheckFunc() {
	v.validate.RegisterValidation("phone", checkPhone)
	v.validate.RegisterValidation("pwd", checkPwd)
	v.validate.RegisterValidation("idCard", checkResidentId)
	v.validate.RegisterValidation("bank-card", checkBankCard)
	v.validate.RegisterValidation("code", checkCode)
	v.validate.RegisterValidation("pin", checkPin)
	v.validate.RegisterValidation("email", checkEmail)
	v.validate.RegisterValidation("img", checkImg)
	v.validate.RegisterValidation("mp4", checkMp4)
	v.validate.RegisterValidation("timeLt", checkTimeLt)
}

// 验证手机号
func checkPhone(fld validator.FieldLevel) bool {
	r1 := regexp.MustCompile(`^13[\d]{9}$|^14[5,7]{1}\d{8}$|^15[^4]{1}\d{8}$|^16[\d]{9}$|^17[2,3,4,5,6,7,8,9]{1}\d{8}$|^18[\d]{9}$|^19[\d]{9}$`)
	return r1.MatchString(fld.Field().String())
}

// 密码
func checkPwd(fld validator.FieldLevel) bool {
	r1 := regexp.MustCompile(`^[\w\S]{6,18}$`)
	return r1.MatchString(fld.Field().String())
}

// 验证码
func checkCode(fld validator.FieldLevel) bool {
	r1 := regexp.MustCompile(`^[\d|A-Z]{6}$`)
	return r1.MatchString(fld.Field().String())
}

// 安全密码
func checkPin(fld validator.FieldLevel) bool {
	r1 := regexp.MustCompile(`^\d{6}$`)
	return r1.MatchString(fld.Field().String())
}

// 身份证号码
func checkResidentId(fld validator.FieldLevel) bool {
	id := strings.ToUpper(strings.TrimSpace(fld.Field().String()))
	if len(id) != 18 {
		return false
	}
	var (
		weightFactor = []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}
		checkCode    = []byte{'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'}
		last         = id[17]
		num          = 0
	)
	for i := 0; i < 17; i++ {
		tmp, err := strconv.Atoi(string(id[i]))
		if err != nil {
			return false
		}
		num = num + tmp*weightFactor[i]
	}
	if checkCode[num%11] != last {
		return false
	}
	r1 := regexp.MustCompile(`(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)`)
	return r1.MatchString(id)
}

// 银行卡
func checkBankCard(fld validator.FieldLevel) bool {
	var (
		sum     = 0
		nDigits = len(fld.Field().String())
		parity  = nDigits % 2
	)
	for i := 0; i < nDigits; i++ {
		var digit = int(fld.Field().String()[i] - 48)
		if i%2 == parity {
			digit *= 2
			if digit > 9 {
				digit -= 9
			}
		}
		sum += digit
	}
	return sum%10 == 0
}

// 验证邮箱
func checkEmail(fld validator.FieldLevel) bool {
	r1 := regexp.MustCompile(`^[a-zA-Z0-9_\-\.]+@[a-zA-Z0-9_\-]+(\.[a-zA-Z0-9_\-]+)+$`)
	return r1.MatchString(fld.Field().String())
}

// 验证图片
func checkImg(fld validator.FieldLevel) bool {
	r1 := regexp.MustCompile(`\.(png|jpg|jpeg)$`)
	return r1.MatchString(fld.Field().String())
}

// 验证mp4
func checkMp4(fld validator.FieldLevel) bool {
	r1 := regexp.MustCompile(`\.mp4$`)
	return r1.MatchString(fld.Field().String())
}

// CheckImg 验证图片
func CheckImg(Img string) bool {
	r1 := regexp.MustCompile(`\.(png|jpg|jpeg|gif)$`)
	return r1.MatchString(Img)
}

// checkTimeLt 验证时间戳是否小于当前时间
func checkTimeLt(fld validator.FieldLevel) bool {
	return fld.Field().Int() <= time.Now().Unix()
}
