package validation

import (
	"errors"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	translations "github.com/go-playground/validator/v10/translations/zh"
)

type Verify struct {
	validate *validator.Validate
	trans    ut.Translator
}

func NewVerify() *Verify {
	zhCh := zh.New()
	uni := ut.New(zhCh, zhCh)
	trans, _ := uni.GetTranslator("zh")
	verify := &Verify{
		validate: validator.New(),
		trans:    trans,
	}
	verify.registerCheckFunc() //注册自定义方法
	_ = translations.RegisterDefaultTranslations(verify.validate, trans)
	verify.registerTagName() //注册自定义字段描述
	verify.registerErrMsg()  //注册自定义验证错误信息
	return verify
}

// 处理错误信息
func (v *Verify) translateFindErr(errs validator.ValidationErrors) error {
	for _, e := range errs {
		return errors.New(e.Translate(v.trans))
	}
	return nil
}

// Struct 传入数据指针
func (v *Verify) Struct(data interface{}) error {
	err := v.validate.Struct(data)
	if err != nil {
		return v.translateFindErr(err.(validator.ValidationErrors))
	}
	return nil
}
