package wechat

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
)

// WXBizDataCrypt 微信小程序数据解密结构体
type (
	WXBizDataCrypt struct {
		appID      string
		sessionKey string
	}

	DeData struct {
		PhoneNumber     string    `json:"phoneNumber"`
		PurePhoneNumber string    `json:"purePhoneNumber"`
		CountryCode     string    `json:"countryCode"`
		Watermark       Watermark `json:"watermark"`
	}
	Watermark struct {
		Timestamp int64  `json:"timestamp"`
		AppID     string `json:"appid"`
	}
)

// NewWXBizDataCrypt 创建 WXBizDataCrypt 实例
func NewWXBizDataCrypt(appID, sessionKey string) *WXBizDataCrypt {
	return &WXBizDataCrypt{
		appID:      appID,
		sessionKey: sessionKey,
	}
}

// DecryptData 解密用户数据
func (w *WXBizDataCrypt) DecryptData(encryptedData, iv string) (*DeData, error) {
	var result DeData
	sessionKey, err := base64.StdEncoding.DecodeString(w.sessionKey)
	if err != nil {
		return &result, err
	}

	aesIV, err := base64.StdEncoding.DecodeString(iv)
	if err != nil {
		return &result, err
	}

	aesCipher, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return &result, err
	}

	block, err := aes.NewCipher(sessionKey)
	if err != nil {
		return &result, err
	}

	mode := cipher.NewCBCDecrypter(block, aesIV)
	mode.CryptBlocks(aesCipher, aesCipher)

	// PKCS7UnPadding
	length := len(aesCipher)
	unpadding := int(aesCipher[length-1])
	aesCipher = aesCipher[:(length - unpadding)]

	err = json.Unmarshal(aesCipher, &result)
	if err != nil {
		return &result, err
	}

	if result.Watermark.AppID != w.appID {
		return &result, errors.New("aes 解密失败")
	}

	return &result, nil
}
