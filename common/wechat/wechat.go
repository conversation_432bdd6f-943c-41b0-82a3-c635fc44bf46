package wechat

import (
	"engine/common/config"
	"engine/common/vine"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"io"
	"time"
)

type (
	WeChats struct {
		Config config.ApiConfig
	}
	OpenidRsp struct {
		Code       int64  `json:"errcode"`
		Msg        string `json:"errmsg"`
		Openid     string `json:"openid"`
		Unionid    string `json:"unionid"`
		SessionKey string `json:"session_key"`
	}

	AccessTokenRsp struct {
		Code        int64  `json:"error_code"`
		Msg         string `json:"error_msg"`
		AccessToken string `json:"access_token"`
	}

	PhoneInfo struct {
		PhoneNumber     string `json:"phoneNumber"`
		PurePhoneNumber string `json:"purePhoneNumber"`
		CountryCode     string `json:"countryCode"`
	}
	PhonenumberRsp struct {
		ErrCode   int       `json:"errcode"`
		ErrMsg    string    `json:"errmsg"`
		PhoneInfo PhoneInfo `json:"phone_info"`
	}
	SubscribeRsp struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
	}
	UserInfo struct {
		TelePhone string
		Openid    string
		Nickname  string
		AvatarUrl string
		Unionid   string
	}

	MsgSecCheckRsp struct {
		ErrCode int                 `json:"errcode"`
		ErrMsg  string              `json:"errmsg"`
		Result  MsgSecCheckResult   `json:"result"`
		Detail  []MsgSecCheckDetail `json:"detail"`
	}
	MsgSecCheckResult struct {
		Suggest string `json:"suggest"`
		Label   int64  `json:"label"`
	}
	MsgSecCheckDetail struct {
		ErrCode  int    `json:"errcode"`
		Strategy string `json:"strategy"`
		Suggest  string `json:"suggest"`
		Label    int64  `json:"label"`
		Keyword  string `json:"keyword"`
	}

	ImgSecCheckRsp struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
	}
)

func NewWeChatService(c config.ApiConfig) *WeChats {
	return &WeChats{Config: c}
}

// 获取openid
func (w *WeChats) GetOpenid(code string) (resp *OpenidRsp, err error) {
	var rsp OpenidRsp
	resp = &rsp

	url := fmt.Sprintf("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		w.Config.WeChat.AppID, w.Config.WeChat.AppSecret, code)

	logx.Infof("GetOpenid: code=%s, appid=%s", code, w.Config.WeChat.AppID)

	_, err = vine.NewDecode(&rsp).De(vine.SetHost().SetTimeout(30 * time.Second).R().Get(url))
	if err != nil {
		return &rsp, fmt.Errorf("调用微信API失败: %v", err)
	}

	logx.Infof("GetOpenid response: errcode=%d, errmsg=%s, openid=%s", rsp.Code, rsp.Msg, rsp.Openid)

	return
}

// 小程序获取用户手机号
func (w *WeChats) GetUserPhonenumber(p_code string) (resp *PhonenumberRsp, err error) {
	var rsp PhonenumberRsp
	resp = &rsp

	logx.Infof("GetUserPhonenumber start: p_code=%s, appid=%s, wechart_url=%s",
		p_code, w.Config.WeChat.AppID, w.Config.ITEM.WECHART_URL)

	res, err := w.GetAccessToken()
	if err != nil {
		logx.Errorf("GetAccessToken failed: %v", err)
		return &rsp, fmt.Errorf("获取access_token失败: %v", err)
	}

	logx.Infof("GetAccessToken result: code=%d, msg=%s, access_token=%s",
		res.Code, res.Msg, res.AccessToken)

	if res.AccessToken == "" {
		return &rsp, fmt.Errorf("access_token为空: code=%d, msg=%s", res.Code, res.Msg)
	}

	logx.Infof("GetUserPhonenumber calling wechat api: p_code=%s, access_token=%s", p_code, res.AccessToken)

	_, err = vine.NewDecode(&rsp).De(vine.SetHost().SetTimeout(30 * time.Second).R().
		SetBody(map[string]interface{}{"code": p_code}).
		Post("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + res.AccessToken))

	if err != nil {
		logx.Errorf("GetUserPhonenumber vine request failed: %v", err)
		return &rsp, fmt.Errorf("调用微信API失败: %v", err)
	}

	logx.Infof("GetUserPhonenumber response: errcode=%d, errmsg=%s, phone=%s",
		rsp.ErrCode, rsp.ErrMsg, rsp.PhoneInfo.PurePhoneNumber)

	return
}

// 获取全局唯一后台接口调用凭据（access_token）
func (w *WeChats) GetAccessToken() (resp *AccessTokenRsp, err error) {
	var rsp AccessTokenRsp
	resp = &rsp

	url := w.Config.ITEM.WECHART_URL + "/wechat/v3/minapp/accesstoken"
	logx.Infof("GetAccessToken calling unified service: url=%s, appid=%s", url, w.Config.WeChat.AppID)

	_, err = vine.NewDecode(&rsp).D(vine.SetHost().SetTimeout(30 * time.Second).R().
		SetQueryParams(map[string]string{
			"appid": w.Config.WeChat.AppID,
		}).Get(url))

	if err != nil {
		logx.Errorf("GetAccessToken unified service failed: %v", err)
		return &rsp, err
	}

	logx.Infof("GetAccessToken response: code=%d, msg=%s, access_token=%s",
		rsp.Code, rsp.Msg, rsp.AccessToken)

	return &rsp, nil
}

// MsgSecCheck 文本内容安全识别
func (w *WeChats) MsgSecCheck(openId, content string) (resp *MsgSecCheckRsp, err error) {
	var rsp MsgSecCheckRsp
	resp = &rsp

	res, _ := w.GetAccessToken()
	if res.AccessToken == "" {
		return &rsp, fmt.Errorf(res.Msg)
	}
	p, err := vine.NewDecode(&rsp).De(vine.SetHost().SetTimeout(30 * time.Second).R().
		SetBody(map[string]interface{}{"openid": openId, "scene": 1, "version": 2, "content": content}).
		Post("https://api.weixin.qq.com/wxa/msg_sec_check?access_token=" + res.AccessToken))
	logx.Info(content, p.String())
	return
}

// ImgSecCheckRsp 文本内容安全识别
func (w *WeChats) ImgSecCheck(fileName string, reader io.Reader) (resp *ImgSecCheckRsp, err error) {
	var rsp ImgSecCheckRsp
	resp = &rsp

	res, _ := w.GetAccessToken()
	if res.AccessToken == "" {
		return &rsp, fmt.Errorf(res.Msg)
	}
	p, err := vine.NewDecode(&rsp).De(vine.SetHost().SetTimeout(30*time.Second).R().
		SetFileReader("media", fileName, reader).
		Post("https://api.weixin.qq.com/wxa/img_sec_check?access_token=" + res.AccessToken))
	logx.Info(p.String())
	return
}

// 小程序获取用户手机号
func (w *WeChats) SendSubscribe(data map[string]interface{}) (resp *SubscribeRsp, err error) {
	var rsp SubscribeRsp
	resp = &rsp

	res, err := w.GetAccessToken()
	if err != nil {
		logx.Errorf("GetAccessToken failed: %v", err)
		return &rsp, fmt.Errorf("获取access_token失败: %v", err)
	}

	logx.Infof("GetAccessToken result: code=%d, msg=%s, access_token=%s",
		res.Code, res.Msg, res.AccessToken)

	if res.AccessToken == "" {
		return &rsp, fmt.Errorf("access_token为空: code=%d, msg=%s", res.Code, res.Msg)
	}

	logx.Infof("SendSubscribe calling wechat api: data=%v, access_token=%s", data, res.AccessToken)

	_, err = vine.NewDecode(&rsp).De(vine.SetHost().SetTimeout(30 * time.Second).R().
		SetBody(data).
		Post("https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + res.AccessToken))

	if err != nil {
		logx.Errorf("SendSubscribe vine request failed: %v", err)
		return &rsp, fmt.Errorf("调用微信API失败: %v", err)
	}

	logx.Infof("SendSubscribe response: errcode=%d, errmsg=%s", rsp.ErrCode, rsp.ErrMsg)

	return
}
