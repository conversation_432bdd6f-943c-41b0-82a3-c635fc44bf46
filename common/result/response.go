package result

type ResponseSuccess struct {
	Code uint32      `json:"error_code"`
	Msg  string      `json:"error_msg"`
	Data interface{} `json:"data"`
}
type NullJson struct{}

func Success(data interface{}) *ResponseSuccess {
	return &ResponseSuccess{0, "OK", data}
}

type ResponseError struct {
	Code uint32 `json:"error_code"`
	Msg  string `json:"error_msg"`
}

func Error(errCode uint32, errMsg string) *ResponseError {
	return &ResponseError{errCode, errMsg}
}
