package result

import (
	"engine/common/logger"
	"engine/common/xerr"
	"fmt"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

func HttpResult(r *http.Request, w http.ResponseWriter, resp interface{}, err error) {
	if err == nil {
		r := Success(resp)
		httpx.WriteJson(w, http.StatusOK, r)
	} else {
		code := http.StatusOK // 默认返回200状态码
		errCode := xerr.ServerCommonError
		errMsg := "服务器开小差啦，稍后再试"

		causeErr := errors.Cause(err)
		if e, ok := causeErr.(*xerr.CodeError); ok {
			errCode = e.GetErrCode()
			errMsg = e.GetErrMsg()
		}

		// 登录无效的情况下保留401状态码
		if errCode == xerr.UserNotExist {
			code = http.StatusUnauthorized
		}

		logger.E("API-ERR", err)

		// 大部分错误返回200状态码，只有登录无效返回401
		httpx.WriteJson(w, code, Error(errCode, errMsg))
	}
}

func ParamErrorResult(r *http.Request, w http.ResponseWriter, err error) {
	errMsg := fmt.Sprintf("%s ,%s", xerr.MapErrMsg(xerr.RequestParamError), err.Error())
	httpx.WriteJson(w, http.StatusOK, Error(xerr.RequestParamError, errMsg))
}
