package winenotes

import "time"

// APIResponse API响应的根结构
type APIResponse struct {
	ErrorCode int         `json:"error_code"`
	ErrorMsg  string      `json:"error_msg"`
	Data      ArticleData `json:"data"`
}

// ArticleData 文章数据结构
type ArticleData struct {
	UUID        string         `json:"uuid"`
	Title       string         `json:"title"`
	Description string         `json:"description"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	Blocks      []ContentBlock `json:"blocks"`
	Author      Author         `json:"author"`
	Tags        []string       `json:"tags"`
}

// Author 作者信息
type Author struct {
	Name   string `json:"name"`
	Avatar string `json:"avatar"`
	Bio    string `json:"bio"`
}

// ContentBlock 内容块基础结构
type ContentBlock struct {
	Type     int    `json:"type"`
	Content  string `json:"content"`
	ByteSize int    `json:"byte_size"`
}

// TextBlock Type 1 - 文本块
type TextBlock struct {
	Content   string     `json:"content"`
	Format    TextFormat `json:"format"`
	Alignment string     `json:"alignment"` // left, center, right
}

// TextFormat 文本格式
type TextFormat struct {
	Color      string  `json:"color"`
	FontSize   int     `json:"font_size"`
	FontWeight string  `json:"font_weight"` // normal, bold
	FontStyle  string  `json:"font_style"`  // normal, italic
	Underline  bool    `json:"underline"`
	LineHeight float64 `json:"line_height"`
}

// ImageBlock Type 2 - 单图块
type ImageBlock struct {
	URL       string `json:"url"`
	Caption   string `json:"caption"`
	Alignment string `json:"alignment"` // left, center, right
	Width     int    `json:"width"`
	Height    int    `json:"height"`
	Alt       string `json:"alt"`
}

// GalleryBlock Type 3 - 多图块/轮播
type GalleryBlock struct {
	Images   []GalleryImage `json:"images"`
	Interval int            `json:"interval"` // 切换间隔（秒）
	AutoPlay bool           `json:"auto_play"`
}

// GalleryImage 轮播图片
type GalleryImage struct {
	URL     string `json:"url"`
	Caption string `json:"caption"`
	Alt     string `json:"alt"`
}

// VideoBlock Type 4 - 视频块
type VideoBlock struct {
	URL      string `json:"url"`
	Poster   string `json:"poster"` // 封面图
	Caption  string `json:"caption"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	AutoPlay bool   `json:"auto_play"`
	Controls bool   `json:"controls"`
}

// WineParamsBlock Type 5 - 酒款参数块
type WineParamsBlock struct {
	Title      string          `json:"title"`
	Parameters []WineParameter `json:"parameters"`
}

// WineParameter 酒款参数
type WineParameter struct {
	Label string `json:"label"`
	Value string `json:"value"`
	Unit  string `json:"unit"`
}
