package winenotes

import (
	"fmt"
	"strings"
)

// HTMLGenerator HTML生成器
type HTMLGenerator struct{}

// NewHTMLGenerator 创建新的HTML生成器
func NewHTMLGenerator() *HTMLGenerator {
	return &HTMLGenerator{}
}

// GenerateSimple 生成简化的HTML内容（仅body内容，无CSS样式）
func (g *HTMLGenerator) GenerateSimple(data *APIResponse) (string, error) {
	err := validateAPIResponse(data)
	if err != nil {
		return "", err
	}

	// 直接生成文章内容，不包含任何HTML文档结构和CSS
	return g.generateSimpleArticleContent(data.Data.Blocks)
}

// generateSimpleArticleContent 生成简化的文章内容（无CSS类和容器）
func (g *HTMLGenerator) generateSimpleArticleContent(blocks []ContentBlock) (string, error) {
	var content strings.Builder

	for i, block := range blocks {
		blockHTML, err := g.generateSimpleContentBlock(block, i)
		if err != nil {
			return "", fmt.Errorf("生成内容块失败 (索引 %d): %w", i, err)
		}
		content.WriteString(blockHTML)
	}

	return content.String(), nil
}

// generateSimpleContentBlock 生成简化的单个内容块（无CSS类和容器）
func (g *HTMLGenerator) generateSimpleContentBlock(block ContentBlock, index int) (string, error) {
	switch block.Type {
	case 1:
		return g.generateSimpleTextBlockHTML(block.Content, index)
	case 2:
		return g.generateSimpleImageBlockHTML(block.Content, index)
	case 3:
		return g.generateSimpleGalleryBlockHTML(block.Content, index)
	case 4:
		return g.generateSimpleVideoBlockHTML(block.Content, index)
	case 5:
		return g.generateSimpleWineParamsBlockHTML(block.Content, index)
	default:
		return "", fmt.Errorf("不支持的内容块类型: %d", block.Type)
	}
}

// generateSimpleTextBlockHTML 生成简化的文本块HTML（根据示例格式）
func (g *HTMLGenerator) generateSimpleTextBlockHTML(content string, _ int) (string, error) {
	textBlock, err := parseTextBlockFromJSON(content)
	if err != nil {
		return "", err
	}

	// 直接返回内容，保持原有的HTML格式
	// 根据示例，文本内容应该保持在<p>标签中，并保留内联样式
	return textBlock.Content, nil
}

// generateSimpleImageBlockHTML 生成简化的图片块HTML（根据示例格式）
func (g *HTMLGenerator) generateSimpleImageBlockHTML(content string, _ int) (string, error) {
	imageBlock, err := parseImageBlockFromJSON(content)
	if err != nil {
		return "", err
	}

	// 根据示例格式，图片应该在<p>标签中，并且可能有下划线样式
	// <p><span style="text-decoration: underline;"><img class="wscnph" src="..." /></span></p>
	return fmt.Sprintf(`<p><span style="text-decoration: underline;"><img class="wscnph" src="%s" /></span></p>`, imageBlock.URL), nil
}

// generateSimpleWineParamsBlockHTML 生成简化的酒款参数块HTML（根据示例格式）
func (g *HTMLGenerator) generateSimpleWineParamsBlockHTML(content string, _ int) (string, error) {
	wineBlock, err := parseWineParamsBlockFromJSON(content)
	if err != nil {
		return "", err
	}

	var html strings.Builder

	// 根据示例格式生成表格
	if len(wineBlock.Parameters) > 0 {
		html.WriteString(`<table style="border-collapse: collapse; width: 100%;" border="1">`)
		html.WriteString(`<tbody>`)

		for _, param := range wineBlock.Parameters {
			value := param.Value
			if param.Unit != "" {
				value = fmt.Sprintf("%s %s", value, param.Unit)
			}
			html.WriteString(fmt.Sprintf(`<tr><td style="width: 50%%;">%s</td><td style="width: 50%%;">%s</td></tr>`, param.Label, value))
		}

		html.WriteString(`</tbody>`)
		html.WriteString(`</table>`)
	}

	return html.String(), nil
}

// generateSimpleGalleryBlockHTML 生成简化的轮播块HTML（根据示例格式）
func (g *HTMLGenerator) generateSimpleGalleryBlockHTML(content string, _ int) (string, error) {
	galleryBlock, err := parseGalleryBlockFromJSON(content)
	if err != nil {
		return "", err
	}

	// 如果没有图片，返回空字符串而不是错误
	if len(galleryBlock.Images) == 0 {
		return "", nil
	}

	var html strings.Builder

	// 对于轮播图，我们简化为多个单独的图片
	for _, image := range galleryBlock.Images {
		html.WriteString(fmt.Sprintf(`<p><span style="text-decoration: underline;"><img class="wscnph" src="%s" /></span></p>`, image.URL))
	}

	return html.String(), nil
}

// generateSimpleVideoBlockHTML 生成简化的视频块HTML（根据示例格式）
func (g *HTMLGenerator) generateSimpleVideoBlockHTML(content string, _ int) (string, error) {
	videoBlock, err := parseVideoBlockFromJSON(content)
	if err != nil {
		return "", err
	}

	// 如果没有视频URL，返回空字符串
	if videoBlock.URL == "" {
		return "", nil
	}

	// 生成video标签，包含封面图
	var videoHTML string
	if videoBlock.Poster != "" {
		videoHTML = fmt.Sprintf(`<p><video src="%s" poster="%s" controls></video></p>`, videoBlock.URL, videoBlock.Poster)
	} else {
		videoHTML = fmt.Sprintf(`<p><video src="%s" controls></video></p>`, videoBlock.URL)
	}

	return videoHTML, nil
}
