package winenotes

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"
)

// Converter JSON到HTML转换器
type Converter struct {
	client *http.Client
}

// NewConverter 创建新的转换器实例
func NewConverter() *Converter {
	return &Converter{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// ConvertToSimpleHTML 将指定UUID的文章转换为简化的HTML文件（仅body内容，无CSS）
// 如果 outputPath 为空字符串，则只返回HTML字符串而不生成文件
func (c *Converter) ConvertToSimpleHTML(uuid, outputPath string) error {
	// 获取API数据
	data, err := c.fetchArticleData(uuid)
	if err != nil {
		return fmt.Errorf("获取文章数据失败: %w", err)
	}

	// 生成简化HTML内容
	html, err := c.generateSimpleHTML(data)
	if err != nil {
		return fmt.Errorf("生成简化HTML失败: %w", err)
	}

	// 如果 outputPath 为空，则不写入文件
	if outputPath != "" {
		// 写入文件
		err = os.WriteFile(outputPath, []byte(html), 0644)
		if err != nil {
			return fmt.Errorf("写入文件失败: %w", err)
		}
	}

	return nil
}

// ConvertToSimpleHTMLString 将指定UUID的文章转换为简化的HTML字符串（仅body内容，无CSS）
// 返回生成的HTML字符串
func (c *Converter) ConvertToSimpleHTMLString(uuid string) (string, error) {
	// 获取API数据
	data, err := c.fetchArticleData(uuid)
	if err != nil {
		return "", fmt.Errorf("获取文章数据失败: %w", err)
	}

	// 生成简化HTML内容
	html, err := c.generateSimpleHTML(data)
	if err != nil {
		return "", fmt.Errorf("生成简化HTML失败: %w", err)
	}

	return html, nil
}

// fetchArticleData 从API获取文章数据
func (c *Converter) fetchArticleData(uuid string) (*APIResponse, error) {
	url := fmt.Sprintf("https://vos.vinehoo.com/wn_api/vinotes/v1/note/detail?uuid=%s", uuid)

	resp, err := c.client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	var apiResp APIResponse
	err = json.Unmarshal(body, &apiResp)
	if err != nil {
		return nil, fmt.Errorf("JSON解析失败: %w", err)
	}

	return &apiResp, nil
}

// generateSimpleHTML 生成简化HTML内容
func (c *Converter) generateSimpleHTML(data *APIResponse) (string, error) {
	generator := NewHTMLGenerator()
	return generator.GenerateSimple(data)
}
