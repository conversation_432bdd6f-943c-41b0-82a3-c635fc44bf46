package winenotes

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
)

// parseTextBlockFromJSON 从JSON字符串解析文本块
func parseTextBlockFromJSON(content string) (*TextBlock, error) {
	// 文本块的content是一个包含ops数组的JSON
	var quillData struct {
		Ops []map[string]interface{} `json:"ops"`
	}

	err := json.Unmarshal([]byte(content), &quillData)
	if err != nil {
		return nil, fmt.Errorf("解析文本块JSON失败: %w", err)
	}

	// 提取文本内容并处理样式
	var htmlContent strings.Builder

	for _, op := range quillData.Ops {
		if insert, ok := op["insert"].(string); ok {
			// 处理文本内容
			text := insert

			// 获取样式属性
			var styles []string
			var alignment string

			if attributes, hasAttrs := op["attributes"].(map[string]interface{}); hasAttrs {
				// 处理字体大小
				if fontSize, ok := attributes["fontSize"].(string); ok {
					styles = append(styles, fmt.Sprintf("font-size: %s", fontSize))
				}

				// 处理粗体
				if bold, ok := attributes["bold"].(string); ok && bold == "strong" {
					text = fmt.Sprintf("<strong>%s</strong>", text)
				}

				// 处理下划线
				if underline, ok := attributes["underline"]; ok && underline == true {
					text = fmt.Sprintf("<u>%s</u>", text)
				}

				// 处理对齐方式
				if align, ok := attributes["align"].(string); ok {
					alignment = align
				}
			}

			// 处理换行符 - 将连续的\n都视为一个换行
			// 使用正则表达式将一个或多个连续的\n替换为<br>
			re := regexp.MustCompile(`\n+`)
			text = re.ReplaceAllString(text, "<br>")

			// 应用样式
			if len(styles) > 0 {
				styleAttr := fmt.Sprintf(` style="%s"`, strings.Join(styles, "; "))
				text = fmt.Sprintf(`<span%s>%s</span>`, styleAttr, text)
			}

			// 应用对齐方式
			if alignment != "" && alignment != "left" {
				htmlContent.WriteString(fmt.Sprintf(`<div style="text-align: %s">%s</div>`, alignment, text))
			} else {
				htmlContent.WriteString(text)
			}
		}
	}

	// 如果内容没有被包装在段落中，添加默认段落标签
	finalContent := htmlContent.String()
	if !strings.Contains(finalContent, "<p>") {
		finalContent = "<p>" + finalContent + "</p>"
	}

	return &TextBlock{
		Content:   finalContent,
		Alignment: "left", // 默认左对齐
		Format: TextFormat{
			FontSize: 16,
			Color:    "#333333",
		},
	}, nil
}

// parseImageBlockFromJSON 从JSON字符串解析图片块
func parseImageBlockFromJSON(content string) (*ImageBlock, error) {
	var imageData struct {
		Path   string `json:"path"`
		Size   int    `json:"size"`
		Align  string `json:"align"`
		Legend string `json:"legend"`
	}

	err := json.Unmarshal([]byte(content), &imageData)
	if err != nil {
		return nil, fmt.Errorf("解析图片块JSON失败: %w", err)
	}

	// 构建完整的URL
	url := imageData.Path
	if !strings.HasPrefix(url, "http") {
		url = "https://images.vinehoo.com" + url
	}

	return &ImageBlock{
		URL:       url,
		Caption:   imageData.Legend,
		Alignment: imageData.Align,
		Alt:       "图片",
	}, nil
}

// parseGalleryBlockFromJSON 从JSON字符串解析轮播块
func parseGalleryBlockFromJSON(content string) (*GalleryBlock, error) {
	// 尝试解析轮播块的JSON数据
	var galleryData struct {
		List []struct {
			Path     string `json:"path"`
			Size     int    `json:"size"`
			IsError  bool   `json:"isError"`
			Progress int    `json:"progress"`
		} `json:"list"`
		Interval int `json:"interval"`
	}

	err := json.Unmarshal([]byte(content), &galleryData)
	if err != nil {
		// 如果解析失败，返回空的轮播块而不是错误
		return &GalleryBlock{
			Images:   []GalleryImage{},
			Interval: 5,
			AutoPlay: true,
		}, nil
	}

	// 转换图片数据
	var images []GalleryImage
	for _, img := range galleryData.List {
		// 跳过错误的图片
		if img.IsError {
			continue
		}

		url := img.Path
		if !strings.HasPrefix(url, "http") {
			url = "https://images.vinehoo.com" + url
		}

		images = append(images, GalleryImage{
			URL:     url,
			Caption: "",
			Alt:     "轮播图片",
		})
	}

	// 将毫秒转换为秒
	interval := galleryData.Interval / 1000
	if interval <= 0 {
		interval = 5 // 默认5秒
	}

	return &GalleryBlock{
		Images:   images,
		Interval: interval,
		AutoPlay: true,
	}, nil
}

// parseVideoBlockFromJSON 从JSON字符串解析视频块
func parseVideoBlockFromJSON(content string) (*VideoBlock, error) {
	var videoData struct {
		Path  string `json:"path"`
		Size  int    `json:"size"`
		Cover struct {
			Path string `json:"path"`
			Size int    `json:"size"`
		} `json:"cover"`
	}

	err := json.Unmarshal([]byte(content), &videoData)
	if err != nil {
		// 如果解析失败，返回空的视频块
		return &VideoBlock{
			URL:      "",
			Controls: true,
			AutoPlay: false,
		}, nil
	}

	// 构建完整的视频URL
	videoURL := videoData.Path
	if !strings.HasPrefix(videoURL, "http") {
		videoURL = "https://images.vinehoo.com" + videoURL
	}

	// 构建封面图URL
	posterURL := videoData.Cover.Path
	if posterURL != "" && !strings.HasPrefix(posterURL, "http") {
		posterURL = "https://images.vinehoo.com" + posterURL
	}

	return &VideoBlock{
		URL:      videoURL,
		Poster:   posterURL,
		Caption:  "",
		Controls: true,
		AutoPlay: false,
	}, nil
}

// parseWineParamsBlockFromJSON 从JSON字符串解析酒款参数块
func parseWineParamsBlockFromJSON(content string) (*WineParamsBlock, error) {
	var wineData map[string]interface{}

	err := json.Unmarshal([]byte(content), &wineData)
	if err != nil {
		return nil, fmt.Errorf("解析酒款参数块JSON失败: %w", err)
	}

	// 参数名称中英文映射
	paramNameMap := map[string]string{
		"name":         "品名",
		"nameEn":       "英文名",
		"country":      "国家",
		"place":        "产区",
		"chateau":      "酒庄",
		"grapeVariety": "葡萄品种",
		"alcohol":      "酒精度",
		"vintage":      "年份",
		"type":         "类型",
		"volume":       "容量",
		"color":        "颜色",
		"style":        "风格",
	}

	var parameters []WineParameter

	// 转换map为参数列表
	for key, value := range wineData {
		if valueStr, ok := value.(string); ok {
			// 使用中文名称，如果没有映射则使用原始key
			chineseName := paramNameMap[key]
			if chineseName == "" {
				chineseName = key
			}

			parameters = append(parameters, WineParameter{
				Label: chineseName,
				Value: valueStr,
				Unit:  "",
			})
		}
	}

	return &WineParamsBlock{
		Title:      "酒款参数",
		Parameters: parameters,
	}, nil
}
