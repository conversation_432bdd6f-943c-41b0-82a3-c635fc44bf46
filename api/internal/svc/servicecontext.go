package svc

import (
	"engine/api/internal/middleware"
	cf "engine/common/config"
	"engine/common/httpClient"
	"engine/common/jwtutil"
	"engine/common/model"
	"engine/common/validation"
	"time"

	red "github.com/gomodule/redigo/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	Config                  cf.ApiConfig
	Verify                  *validation.Verify
	Global                  rest.Middleware
	Auth                    rest.Middleware
	Admin                   rest.Middleware
	MerchantAuth            rest.Middleware
	Jwt                     *jwtutil.JWTUtil
	MerchantJwt             *jwtutil.JWTUtil
	Redis                   *red.Pool
	TransModel              model.TransModel
	BatchModel              model.VhBatchModel
	BatchRegionConfModel    model.VhBatchRegionConfModel
	MerchantModel           model.VhMerchantModel
	MerchantPointsModel     model.VhMerchantPointsModel
	MerchantRedemptionModel model.VhMerchantRedemptionModel
	PrizeConfModel          model.VhPrizeConfModel
	PrizeRecordModel        model.VhPrizeRecordModel
	UserModel               model.VhUserModel
	AppletAppid             string
	AppletSecret            string
	HttpClient              *httpClient.HttpConfig
}

func NewServiceContext(c cf.ApiConfig) *ServiceContext {
	mysql := sqlx.NewMysql(c.Mysql.DataSource)
	srv := &ServiceContext{
		Config:      c,
		Verify:      validation.NewVerify(),
		Global:      middleware.NewGlobalMiddleware().Handle,
		Admin:       middleware.NewAdminMiddleware().Handle,
		Jwt:         jwtutil.NewJWTUtil(c.Auth.AccessSecret, c.Auth.AccessExpire),
		MerchantJwt: jwtutil.NewJWTUtil(c.Auth.MerchantAccessSecret, c.Auth.MerchantAccessExpire),
		Redis: &red.Pool{
			MaxIdle:   100,
			MaxActive: 100,
			Dial: func() (red.Conn, error) {
				return red.Dial("tcp", c.Redis[0].Host, red.DialPassword(c.Redis[0].Pass),
					red.DialDatabase(12),
					red.DialConnectTimeout(time.Second), //链接超时
					red.DialWriteTimeout(time.Second*1), //写超时
					red.DialReadTimeout(time.Second*1),  //读超时
				)
			},
		},
		TransModel:              model.NewTransModel(mysql),
		BatchModel:              model.NewVhBatchModel(mysql),
		BatchRegionConfModel:    model.NewVhBatchRegionConfModel(mysql),
		MerchantModel:           model.NewVhMerchantModel(mysql),
		MerchantPointsModel:     model.NewVhMerchantPointsModel(mysql),
		MerchantRedemptionModel: model.NewVhMerchantRedemptionModel(mysql),
		PrizeConfModel:          model.NewVhPrizeConfModel(mysql),
		PrizeRecordModel:        model.NewVhPrizeRecordModel(mysql),
		UserModel:               model.NewVhUserModel(mysql),
		AppletAppid:             "",
		AppletSecret:            "",
		HttpClient:              httpClient.Handle(c),
	}
	srv.Auth = middleware.NewAuthMiddleware(srv.Jwt).Handle
	srv.MerchantAuth = middleware.NewMerchantAuthMiddleware(srv.MerchantJwt).Handle

	return srv
}

func (c *ServiceContext) Close() {
}
