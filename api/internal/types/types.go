// Code generated by goctl. DO NOT EDIT.
package types

type BatchAddReq struct {
	Title     string `json:"title" validate:"max=50" v:"批次名称"`
	ShortCode string `json:"short_code" validate:"max=20" v:"简码"`
	GoodsName string `json:"goods_name" validate:"max=50" v:"产品名称"`
	Desc      string `json:"desc,optional" validate:"max=250" v:"描述"`
	StartTime string `json:"start_time" validate:"required" v:"开始时间"`
	EndTime   string `json:"end_time" validate:"required" v:"结束时间"`
}

type BatchChangeReq struct {
	IdJU
	IsEnable bool `json:"is_enable" v:"是否启用"`
}

type BatchInfo struct {
	Id         uint64 `json:"id"`
	Title      string `json:"title"`       // 批次名称
	ShortCode  string `json:"short_code"`  // 简码
	GoodsName  string `json:"goods_name"`  // 产品名称
	Desc       string `json:"desc"`        // 描述
	Status     uint64 `json:"status"`      // 1启用，2禁用
	CreateTime string `json:"create_time"` // 创建时间
	StartTime  string `json:"start_time"`  // 开始时间
	EndTime    string `json:"end_time"`    // 结束时间
}

type BatchListReq struct {
	Paging
	Status uint64 `form:"status,optional"`
}

type BatchListResp struct {
	List  []BatchInfo `json:"list"`
	Total int64       `json:"total"`
}

type IdF struct {
	Id int64 `form:"id" validate:"required" v:"数据id"`
}

type IdFU struct {
	Id uint64 `form:"id" validate:"required" v:"数据id"`
}

type IdJ struct {
	Id int64 `json:"id" validate:"required" v:"数据id"`
}

type IdJU struct {
	Id uint64 `json:"id" validate:"required" v:"数据id"`
}

type MerchantBindingReq struct {
	CompanyName          string `json:"company_name" validate:"max=50" v:"公司名称"`
	ContactName          string `json:"contact_name" validate:"max=10" v:"联系人姓名"`
	UnifiedSocialCode    string `json:"unified_social_code" validate:"required" v:"社会统一代码"`
	BusinessLicenseImage string `json:"business_license_image" validate:"required" v:"营业执照图片"`
	ProvinceId           uint64 `json:"province_id" validate:"required" v:"省id"`
	CityId               uint64 `json:"city_id" validate:"required" v:"市id"`
	DistrictId           uint64 `json:"district_id" validate:"required" v:"区id"`
	ProvinceName         string `json:"province_name" validate:"required" v:"省名称"`
	CityName             string `json:"city_name" validate:"required" v:"市名称"`
	DistrictName         string `json:"district_name" validate:"required" v:"区名称"`
	Address              string `json:"address" validate:"max=250" v:"详细地址"`
}

type MerchantChangeReq struct {
	IdJU
	IsPass bool   `json:"is_pass" v:"是否通过"`
	Reason string `json:"reason,optional" validate:"max=250" v:"驳回原因"`
}

type MerchantInfo struct {
	Id                   uint64 `json:"id"`
	Phone                string `json:"phone"`                  // 手机号
	CompanyName          string `json:"company_name"`           // 公司名称
	ContactName          string `json:"contact_name"`           // 联系人姓名
	UnifiedSocialCode    string `json:"unified_social_code"`    // 社会统一代码
	BusinessLicenseImage string `json:"business_license_image"` // 营业执照图片
	ProvinceId           uint64 `json:"province_id"`            // 省id
	CityId               uint64 `json:"city_id"`                // 市id
	DistrictId           uint64 `json:"district_id"`            // 区id
	ProvinceName         string `json:"province_name"`          // 省名称
	CityName             string `json:"city_name"`              // 市名称
	DistrictName         string `json:"district_name"`          // 区名称
	Address              string `json:"address"`                // 完整地址(包含省市区)
	CreateTime           string `json:"create_time"`            // 创建时间
	Status               uint64 `json:"status"`                 // 1未提交，2审核中，3通过，4驳回（可以再提交）
	Reason               string `json:"reason"`                 // 驳回原因
	LastLoginTime        string `json:"last_login_time"`        // 最后登录时间
}

type MerchantListReq struct {
	Paging
	Keyword     string `form:"keyword,optional"`
	OrderColumn string `form:"order_column,optional"`
	OrderBy     string `form:"order_by,optional"`
}

type MerchantListResp struct {
	List  []MerchantInfo `json:"list"`
	Total int64          `json:"total"`
}

type MiniPayResp struct {
	AppId     string `json:"app_id"`
	TimeStamp string `json:"time_stamp"`
	NonceStr  string `json:"nonce_str"`
	Package   string `json:"package"`
	SignType  string `json:"sign_type"`
	PaySign   string `json:"pay_sign"`
}

type Paging struct {
	Page  int64 `form:"page,default=1"`
	Limit int64 `form:"limit,default=10"`
}

type PrizeConfAddReq struct {
	BatchId        uint64 `json:"batch_id" validate:"required" v:"批次id"`
	RegionCode     string `json:"region_code" validate:"required" v:"大区code"`
	PrizeName      string `json:"prize_name" validate:"required" v:"奖品名"`
	Status         uint64 `json:"status" validate:"required" v:"状态1启用，2禁用"`
	Points         uint64 `json:"points" validate:"required" v:"积分（单位分，中奖一次可以兑换多少分）"`
	RedeemQuantity uint64 `json:"redeem_quantity" validate:"required" v:"总中奖瓶数(单位瓶)"`
	PrizeCt        uint64 `json:"prize_ct" validate:"required" v:"总中奖次数"`
	ValidDays      uint64 `json:"valid_days" validate:"required" v:"有效天数(多少天后过期)"`
}

type PrizeConfDetailReq struct {
	IdFU
}

type PrizeConfDetailResp struct {
	RedeemCt       uint64                `json:"total_ct"`         //真实总中奖瓶数汇总
	WinCt          float64               `json:"win_ct"`           //已中奖瓶数汇总
	RemainCt       float64               `json:"remain_ct"`        //总剩余瓶数汇总
	RegionConfList []PrizeRegionConfInfo `json:"region_conf_list"` //大区中奖配置
}

type PrizeConfUpdareReq struct {
	IdJU
	PrizeName      string `json:"prize_name" validate:"required" v:"奖品名"`
	Status         uint64 `json:"status" validate:"required" v:"状态1启用，2禁用"`
	Points         uint64 `json:"points" validate:"required" v:"积分（单位分，中奖一次可以兑换多少分）"`
	RedeemQuantity uint64 `json:"redeem_quantity" validate:"required" v:"总中奖瓶数(单位瓶)"`
	PrizeCt        uint64 `json:"prize_ct" validate:"required" v:"总中奖次数"`
	ValidDays      uint64 `json:"valid_days" validate:"required" v:"有效天数(多少天后过期)"`
}

type PrizeInfo struct {
	Id             uint64 `json:"id"`
	BatchId        uint64 `json:"batch_id"`        // 批次id
	RegionCode     string `json:"region_code"`     // 大区code
	RegionName     string `json:"region_name"`     // 大区名称
	PrizeName      string `json:"prize_name"`      // 奖品名
	Status         uint64 `json:"status"`          // 状态1启用，2禁用
	Points         uint64 `json:"points"`          // 积分（单位分，中奖一次可以兑换多少分）
	RedeemQuantity uint64 `json:"redeem_quantity"` // 总中奖瓶数(单位瓶)
	PrizeCt        uint64 `json:"prize_ct"`        // 总中奖次数
	WinCt          uint64 `json:"win_ct"`          // 已中奖次数
	ValidDays      uint64 `json:"valid_days"`      // 有效天数(多少天后过期)
}

type PrizeRecordListInfo struct {
	Id             uint64 `json:"id"`
	Phone          string `json:"phone"`           // 手机号
	ShortCode      string `json:"short_code"`      // 简码
	GoosName       string `json:"goos_name"`       // 商品名称
	PrizeName      string `json:"prize_name"`      // 奖品名
	PrizeTime      string `json:"prize_time"`      // 中奖时间
	Status         uint64 `json:"status"`          // 状态(1未核销，2已核销，3已过期)
	CompanyName    string `json:"company_name"`    // 核销公司名称
	RedemptionTime string `json:"redemption_time"` // 核销时间
}

type PrizeRecordListReq struct {
	Paging
	Keyword             string `form:"keyword,optional"`
	Status              int64  `form:"status,optional"`
	OrderColumn         string `form:"order_column,optional"`
	OrderBy             string `form:"order_by,optional"`
	PrizeStartTime      string `form:"prize_start_time,optional"`
	PrizeEndTime        string `form:"prize_end_time,optional"`
	RedemptionStartTime string `form:"redemption_start_time,optional"`
	RedemptionEndTime   string `form:"redemption_end_time,optional"`
}

type PrizeRecordListResp struct {
	List            []PrizeRecordListInfo `json:"list"`
	Total           int64                 `json:"total"`            //总数
	RedemptionCount int64                 `json:"redemption_count"` //已核销数
	UnverifiedCount int64                 `json:"unverified_count"` //未核销数
}

type PrizeRegionConfInfo struct {
	RegionCode string      `json:"region_code"` //大区编号
	RegionName string      `json:"region_name"` //大区名称
	TotalCt    uint64      `json:"total_ct"`    //配置总中奖瓶数汇总
	RedeemCt   uint64      `json:"redeem_ct"`   //真实总中奖瓶数汇总
	WinCt      float64     `json:"win_ct"`      //已中奖瓶数汇总
	RemainCt   float64     `json:"remain_ct"`   //总剩余瓶数汇总
	PrizeList  []PrizeInfo `json:"prize_list"`  //奖品列表
}

type RedemptionListInfo struct {
	Id             uint64 `json:"id"`
	CompanyName    string `json:"company_name"`    // 公司名称
	ShortCode      string `json:"short_code"`      // 商品简码
	GoodsName      string `json:"goods_name"`      // 商品名
	RedeemQuantity uint64 `json:"redeem_quantity"` // 兑换瓶数
	ProvinceName   string `json:"province_name"`   // 省名称
	CityName       string `json:"city_name"`       // 市名称
	DistrictName   string `json:"district_name"`   // 区名称
	Address        string `json:"address"`         // 完整收货地址(包含省市区)
	Consignee      string `json:"consignee"`       // 收货人
	ContactPhone   string `json:"contact_phone"`   // 联系人电话
	CreateTime     string `json:"create_time"`     // 申请时间
	Status         uint64 `json:"status"`          // 1待发货，2已发货，3已签收
	ShippingCode   string `json:"shipping_code"`   // 物流单号
	ShipTime       string `json:"ship_time"`       // 发货时间
}

type RedemptionListReq struct {
	Paging
	Keyword     string `form:"keyword,optional"`
	Status      int64  `form:"status,optional"`
	OrderColumn string `form:"order_column,optional"`
	OrderBy     string `form:"order_by,optional"`
	StartTime   string `json:"start_time,optional"`
	EndTime     string `json:"end_time,optional"`
}

type RedemptionListResp struct {
	List  []RedemptionListInfo `json:"list"`
	Total int64                `json:"total"`
}

type WeChatAppletPhoneLoginReq struct {
	Code  string `json:"code" validate:"required"`
	Pcode string `json:"p_code" validate:"required"`
}

type WeChatAppletPhoneLoginResp struct {
	Token string `json:"token"`
}
