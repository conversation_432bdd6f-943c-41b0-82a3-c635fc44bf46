package middleware

import (
	"context"
	"engine/common/jwtutil"
	"net/http"
)

type AuthMiddleware struct {
	Jwt *jwtutil.JWTUtil
}

func NewAuthMiddleware(jwt *jwtutil.JWTUtil) *AuthMiddleware {
	return &AuthMiddleware{Jwt: jwt}
}

func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		tokenString := r.Header.Get("Authorization")
		if tokenString == "" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		claims, err := m.Jwt.VerifyToken(tokenString)
		if err != nil {
			http.Error(w, err.Error(), http.StatusUnauthorized)
			return
		}
		r = r.WithContext(context.WithValue(r.Context(), "jwt_user", claims))
		next(w, r)
	}
}
