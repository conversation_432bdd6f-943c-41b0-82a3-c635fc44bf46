package middleware

import (
	"context"
	"encoding/base64"
	"net/http"
	"strconv"
)

type AdminMiddleware struct {
}

func NewAdminMiddleware() *AdminMiddleware {
	return &AdminMiddleware{}
}

func (m *AdminMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 获取vinehoo-uid
		uidStr := r.Header.Get("vinehoo-uid")
		if uidStr == "" {
			http.Error(w, "未登录", http.StatusUnauthorized)
			return
		}

		uid, err := strconv.ParseInt(uidStr, 10, 64)
		if err != nil || uid <= 0 {
			http.Error(w, "未登录", http.StatusUnauthorized)
			return
		}

		// 获取vinehoo-vos-name并base64解码
		vosNameEncoded := r.Header.Get("vinehoo-vos-name")
		if vosNameEncoded == "" {
			http.Error(w, "未登录", http.StatusUnauthorized)
			return
		}

		vosNameBytes, err := base64.StdEncoding.DecodeString(vosNameEncoded)
		if err != nil {
			http.Error(w, "用户名格式错误", http.StatusUnauthorized)
			return
		}
		vosName := string(vosNameBytes)

		// 将用户信息存入context
		ctx := context.WithValue(r.Context(), "admin_uid", uid)
		ctx = context.WithValue(ctx, "admin_vos_name", vosName)
		r = r.WithContext(ctx)

		next(w, r)
	}
}
