package logic

// 省份信息
type Province struct {
	ID   int    // 省份ID
	Name string // 省份名称
}

// 大区配置
type RegionConfig struct {
	Code      string     // 大区代码
	Name      string     // 大区名称
	Provinces []Province // 包含的省份
}

// 全局地区配置
var (
	regions          []RegionConfig
	provinceToRegion map[int]string // 省份ID到大区code的映射
)

// 初始化地区配置
func init() {
	// 1. 定义所有大区及其包含的省份
	regions = []RegionConfig{
		{
			Code: "NC",
			Name: "华北",
			Provinces: []Province{
				{ID: 11, Name: "北京"},
				{ID: 12, Name: "天津"},
				{ID: 13, Name: "河北"},
				{ID: 14, Name: "山西"},
				{ID: 15, Name: "内蒙古"},
			},
		},
		{
			Code: "EC",
			Name: "华东",
			Provinces: []Province{
				{ID: 31, Name: "上海"},
				{ID: 32, Name: "江苏"},
				{ID: 33, Name: "浙江"},
				{ID: 34, Name: "安徽"},
				{ID: 35, Name: "福建"},
				{ID: 36, Name: "江西"},
				{ID: 37, Name: "山东"},
			},
		},
		// 可以继续添加其他大区...
	}

	// 2. 构建省份到大区的映射
	provinceToRegion = make(map[int]string)
	for _, region := range regions {
		for _, province := range region.Provinces {
			provinceToRegion[province.ID] = region.Code
		}
	}
}

// 获取所有大区配置
func GetAllRegions() []RegionConfig {
	return regions
}

// 通过大区ID获取大区信息
func GetRegionByID(regionCode string) *RegionConfig {
	for _, region := range regions {
		if region.Code == regionCode {
			return &region
		}
	}
	return nil
}

// 通过省份ID获取所属大区
func GetRegionByProvinceID(provinceID int) *RegionConfig {
	regionID, ok := provinceToRegion[provinceID]
	if !ok {
		return nil
	}
	return GetRegionByID(regionID)
}

// 通过省份名称获取所属大区
func GetRegionByProvinceName(provinceName string) *RegionConfig {
	for _, region := range regions {
		for _, province := range region.Provinces {
			if province.Name == provinceName {
				return &region
			}
		}
	}
	return nil
}
