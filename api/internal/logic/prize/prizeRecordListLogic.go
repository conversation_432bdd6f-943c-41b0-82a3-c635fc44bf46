package prize

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeRecordListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeRecordListLogic {
	return &PrizeRecordListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeRecordListLogic) PrizeRecordList(req *types.PrizeRecordListReq) (resp *types.PrizeRecordListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
