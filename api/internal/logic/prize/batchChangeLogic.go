package prize

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchChangeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchChangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchChangeLogic {
	return &BatchChangeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchChangeLogic) BatchChange(req *types.BatchChangeReq) error {
	// 查找批次
	batch, err := l.svcCtx.BatchModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			l.<PERSON>("BatchChange.BatchModel.FindOne err: %v", err)
		}
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 更新状态
	if req.IsEnable {
		if batch.Status == 1 {
			return nil
		}
		batch.Status = 1 // 启用
	} else {
		if batch.Status == 2 {
			return nil
		}
		batch.Status = 2 // 禁用
	}

	// 保存更新
	err = l.svcCtx.BatchModel.Update(l.ctx, batch)
	if err != nil {
		l.Errorf("BatchChange.BatchModel.Update err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil

}
