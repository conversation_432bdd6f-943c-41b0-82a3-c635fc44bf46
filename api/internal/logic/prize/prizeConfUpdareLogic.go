package prize

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeConfUpdareLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeConfUpdareLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeConfUpdareLogic {
	return &PrizeConfUpdareLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeConfUpdareLogic) PrizeConfUpdare(req *types.PrizeConfUpdareReq) error {
	// todo: add your logic here and delete this line

	return nil
}
