package prize

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchAddLogic {
	return &BatchAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchAddLogic) BatchAdd(req *types.BatchAddReq) error {
	// 解析开始时间和结束时间
	startTime, err := common.ParseTime(req.StartTime)
	if err != nil {
		l.Errorf("BatchAdd.ParseTime startTime err: %v", err)
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "开始时间格式错误")
	}

	endTime, err := common.ParseTime(req.EndTime)
	if err != nil {
		l.Errorf("BatchAdd.ParseTime endTime err: %v", err)
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "结束时间格式错误")
	}

	// 验证时间逻辑
	if !endTime.After(startTime) {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "结束时间必须大于开始时间")
	}

	// 创建批次数据
	batch := &model.VhBatch{
		Title:     req.Title,
		ShortCode: req.ShortCode,
		GoodsName: req.GoodsName,
		Desc:      req.Desc,
		Status:    1, // 默认启用状态
		StartTime: startTime,
		EndTime:   endTime,
	}

	// 插入数据库
	_, err = l.svcCtx.BatchModel.Insert(l.ctx, batch)
	if err != nil {
		l.Errorf("BatchAdd.BatchModel.Insert err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
