package prize

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchListLogic {
	return &BatchListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchListLogic) BatchList(req *types.BatchListReq) (resp *types.BatchListResp, err error) {
	var (
		wait  errgroup.Group
		total int64
		list  []model.VhBatch
	)

	// 构建查询条件
	where := squirrel.And{}

	if req.Status != 0 {
		where = append(where, squirrel.Eq{"status": req.Status})
	}

	// 获取总数
	wait.Go(func() error {
		builder := model.CountBuilder("*", l.svcCtx.BatchModel.TableName()).Where(where)
		ct, er := l.svcCtx.BatchModel.FindCount(l.ctx, builder)
		if er != nil {
			l.Errorf("BatchList.BatchModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		builder := l.svcCtx.BatchModel.RowBuilder().Where(where).
			Offset(model.GetOffset(req.Page, req.Limit)).
			Limit(uint64(req.Limit)).
			OrderBy("create_time DESC")

		er := l.svcCtx.BatchModel.FindRows(l.ctx, builder, &list)
		if er != nil {
			l.Errorf("BatchList.BatchModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.BatchListResp)
	resp.Total = total
	resp.List = make([]types.BatchInfo, 0, len(list))
	for _, ls := range list {
		info := types.BatchInfo{
			Id:         ls.Id,
			Title:      ls.Title,
			ShortCode:  ls.ShortCode,
			GoodsName:  ls.GoodsName,
			Desc:       ls.Desc,
			Status:     ls.Status,
			CreateTime: common.TimeToString(ls.CreateTime),
			StartTime:  common.TimeToString(ls.StartTime),
			EndTime:    common.TimeToString(ls.EndTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
