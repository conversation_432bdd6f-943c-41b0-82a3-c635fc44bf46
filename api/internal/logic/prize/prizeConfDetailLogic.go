package prize

import (
	"context"
	"engine/api/internal/logic"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeConfDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeConfDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeConfDetailLogic {
	return &PrizeConfDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeConfDetailLogic) PrizeConfDetail(req *types.PrizeConfDetailReq) (resp *types.PrizeConfDetailResp, err error) {
	//获取所有奖项
	var list []model.VhPrizeConf
	err = l.svcCtx.PrizeConfModel.FindRows(l.ctx, l.svcCtx.PrizeConfModel.RowBuilder().Where("batch_id = ?", req.Id), &list)
	if err != nil {
		l.Errorf("PrizeConfDetail.PrizeConfModel.FindRows err: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "获取奖品配置失败")
	}

	//获取所有大区配置的总中奖瓶数
	var regionList []model.VhBatchRegionConf
	err = l.svcCtx.BatchRegionConfModel.FindRows(l.ctx, l.svcCtx.BatchRegionConfModel.RowBuilder().Where("batch_id = ?", req.Id), &regionList)
	if err != nil {
		l.Errorf("PrizeConfDetail.BatchRegionConfModel.FindRows err: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "获取大区配置失败")
	}
	regionMap := make(map[string]uint64)
	for _, region := range regionList {
		regionMap[region.RegionCode] += region.PrizeCt
	}

	//所有大区
	regions := logic.GetAllRegions()

	// 按大区代码分组奖品配置
	regionPrizeMap := make(map[string][]model.VhPrizeConf)
	prizeIds := make([]uint64, 0, len(list))
	for _, prize := range list {
		regionPrizeMap[prize.RegionCode] = append(regionPrizeMap[prize.RegionCode], prize)
		prizeIds = append(prizeIds, prize.Id)
	}

	resp = new(types.PrizeConfDetailResp)
	resp.RegionConfList = make([]types.PrizeRegionConfInfo, 0, len(regions))

	// 遍历所有大区，确保每个大区都有数据（即使没有配置奖品）
	for _, region := range regions {
		regionInfo := types.PrizeRegionConfInfo{
			RegionCode: region.Code,
			RegionName: region.Name,
			TotalCt:    0,
			WinCt:      0,
			RemainCt:   0,
			RedeemCt:   0,
			PrizeList:  make([]types.PrizeInfo, 0),
		}

		// 如果该大区有奖品配置，则处理这些配置
		if prizes, exists := regionPrizeMap[region.Code]; exists {
			regionInfo.PrizeList = make([]types.PrizeInfo, 0, len(prizes))

			for _, prize := range prizes {
				prizeInfo := types.PrizeInfo{
					Id:             prize.Id,
					BatchId:        prize.BatchId,
					RegionCode:     prize.RegionCode,
					RegionName:     prize.RegionName,
					PrizeName:      prize.PrizeName,
					Status:         prize.Status,
					Points:         prize.Points,
					RedeemQuantity: prize.RedeemQuantity,
					PrizeCt:        prize.PrizeCt,
					WinCt:          prize.WinCt,
					ValidDays:      prize.ValidDays,
				}
				regionInfo.PrizeList = append(regionInfo.PrizeList, prizeInfo)

				// 累计大区统计数据
				regionInfo.RedeemCt += prize.RedeemQuantity
				//转位瓶，Points单位是分
				regionInfo.WinCt += float64(prize.WinCt) * (float64(prize.Points) / 100)
			}
		}

		// 获取大区配置的总中奖瓶数
		if ct, ok := regionMap[region.Code]; ok {
			regionInfo.TotalCt = ct
		}

		// 计算剩余数量
		regionInfo.RemainCt = float64(regionInfo.RedeemCt) - regionInfo.WinCt
		resp.RegionConfList = append(resp.RegionConfList, regionInfo)

		// 累计总体统计数据
		resp.RedeemCt += regionInfo.RedeemCt
		resp.WinCt += regionInfo.WinCt
		resp.RemainCt += regionInfo.RemainCt
	}

	return
}
