package prize

import (
	"context"
	"engine/api/internal/logic"
	"engine/common/model"
	"engine/common/xerr"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeConfDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeConfDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeConfDetailLogic {
	return &PrizeConfDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeConfDetailLogic) PrizeConfDetail(req *types.PrizeConfDetailReq) (resp *types.PrizeConfDetailResp, err error) {
	var list []model.VhPrizeConf
	err = l.svcCtx.PrizeConfModel.FindRows(l.ctx, l.svcCtx.PrizeConfModel.RowBuilder().Where("batch_id = ?", req.Id), &list)
	if err != nil {
		l.Errorf("PrizeConfDetail.PrizeConfModel.FindRows err: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "获取奖品配置失败")
	}

	regions := logic.GetAllRegions()

	resp = new(types.PrizeConfDetailResp)
	resp.RegionConfList = make([]types.PrizeRegionConfInfo, 0, len(regions))

	return
}
