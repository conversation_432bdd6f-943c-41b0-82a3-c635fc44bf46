package prize

import (
	"context"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PrizeConfAddLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPrizeConfAddLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeConfAddLogic {
	return &PrizeConfAddLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeConfAddLogic) PrizeConfAdd(req *types.PrizeConfAddReq) error {
	// todo: add your logic here and delete this line

	return nil
}
