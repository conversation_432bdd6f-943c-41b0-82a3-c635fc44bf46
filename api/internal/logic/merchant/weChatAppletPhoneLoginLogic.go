package merchant

import (
	"context"
	"engine/api/internal/service/wx"
	"engine/common"
	"engine/common/logger"
	"engine/common/model"
	"engine/common/xerr"
	"errors"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WeChatAppletPhoneLoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWeChatAppletPhoneLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WeChatAppletPhoneLoginLogic {
	return &WeChatAppletPhoneLoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WeChatAppletPhoneLoginLogic) WeChatAppletPhoneLogin(req *types.WeChatAppletPhoneLoginReq) (resp *types.WeChatAppletPhoneLoginResp, err error) {
	// 1. 解密微信用户信息和手机号
	wxLogic := wx.NewWxLogic(l.svcCtx)
	userInfo, err := wxLogic.GetUserInfo(req.Code, req.Pcode)
	if err != nil {
		logger.E("微信用户信息获取失败: " + err.Error())
		return nil, xerr.NewErrMsg("微信用户信息获取失败: ")
	}

	// 2. 查找用户（手机号唯一，且未删除）
	user, err := l.svcCtx.MerchantModel.FindOneByPhone(l.ctx, userInfo.Telephone)
	if err != nil && !errors.Is(err, model.ErrNotFound) {
		return nil, xerr.NewErrMsg("查找用户失败 ")
	}
	if err == nil && user.Id > 0 {
		user.LastLoginTime = time.Now()
		//以用户最新的openid为准 同一个微信可以选择多个手机号登录
		if user.OpenId != userInfo.OpenID {
			user.OpenId = userInfo.OpenID
		}
	} else {
		user = &model.VhMerchant{
			OpenId:        userInfo.OpenID,
			Phone:         userInfo.Telephone,
			LastLoginTime: time.Now(),
		}
		row, err := l.svcCtx.MerchantModel.Insert(l.ctx, user)
		if err != nil {
			l.Errorf("WeChatAppletPhoneLogin MerchantModel.Insert err: %v", err)
			return nil, xerr.NewErrMsg("保存用户失败 ")
		}
		uid, err := row.LastInsertId()
		if err != nil {
			return nil, xerr.NewErrMsg("保存用户失败 ")
		}
		user.Id = uint64(uid)
	}

	token, err := l.svcCtx.MerchantJwt.GenerateToken(map[string]interface{}{
		"uid":        user.Id,
		"login_time": common.TimeToString(user.LastLoginTime),
	})
	if err != nil {
		return nil, xerr.NewErrMsg("token生成失败")
	}

	resp = &types.WeChatAppletPhoneLoginResp{
		Token: token,
	}
	return
}
