package merchant

import (
	"context"
	"engine/common/model"
	"engine/common/xerr"
	"errors"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewChangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeLogic {
	return &ChangeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ChangeLogic) Change(req *types.MerchantChangeReq) error {
	if !req.IsPass && req.Reason == "" {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "请输入驳回原因")
	}

	merchant, err := l.svcCtx.MerchantModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			l.<PERSON>("Change.MerchantModel.FindOne err: %v", err)
		}
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	if merchant.Status != 2 {
		return xerr.NewErrCodeMsg(xerr.RequestParamError, "状态不正确")
	}

	if req.IsPass {
		merchant.Status = 3
	} else {
		merchant.Status = 4
		merchant.Reason = req.Reason
	}

	err = l.svcCtx.MerchantModel.Update(l.ctx, merchant)
	if err != nil {
		l.Errorf("Change.MerchantModel.Update err: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
