package merchant

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MerchantListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMerchantListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MerchantListLogic {
	return &MerchantListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MerchantListLogic) MerchantList(req *types.MerchantListReq) (resp *types.MerchantListResp, err error) {
	var (
		wait  errgroup.Group
		total int64
		list  []model.VhMerchant
	)

	// 构建查询条件
	where := squirrel.And{}
	// 添加搜索条件
	if req.Keyword != "" {
		where = append(where, squirrel.Or{
			squirrel.Like{"phone": "%" + req.Keyword + "%"},
			squirrel.Like{"company_name": "%" + req.Keyword + "%"},
			squirrel.Like{"contact_name": "%" + req.Keyword + "%"},
			squirrel.Like{"address": "%" + req.Keyword + "%"},
		})
	}

	// 获取总数
	wait.Go(func() error {
		ct, er := l.svcCtx.MerchantModel.FindCount(l.ctx, model.CountBuilder("*", l.svcCtx.MerchantModel.TableName()).Where(where))
		if er != nil {
			l.Errorf("MerchantList.MerchantModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		builder := l.svcCtx.MerchantModel.RowBuilder().
			Where(where).
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit))

		if req.OrderBy != "" && req.OrderColumn != "" && common.InSlice(req.OrderColumn, []string{"company_name", "status"}) && common.InSlice(req.OrderBy, []string{"asc", "desc"}) {
			builder = builder.OrderBy(fmt.Sprintf("%s %s", req.OrderColumn, req.OrderColumn))
		} else {
			builder = builder.OrderBy("create_time DESC")
		}

		er := l.svcCtx.MerchantModel.FindRows(l.ctx, builder, &list)
		if er != nil {
			l.Errorf("MerchantList.MerchantList.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.MerchantListResp)
	resp.Total = total
	resp.List = make([]types.MerchantInfo, 0, len(list))
	for _, ls := range list {
		info := types.MerchantInfo{
			Id:                   ls.Id,
			Phone:                ls.Phone,
			CompanyName:          ls.CompanyName,
			ContactName:          ls.ContactName,
			UnifiedSocialCode:    ls.UnifiedSocialCode,
			BusinessLicenseImage: l.svcCtx.Config.ITEM.ALIURL + ls.BusinessLicenseImage,
			ProvinceId:           ls.ProvinceId,
			CityId:               ls.CityId,
			DistrictId:           ls.DistrictId,
			ProvinceName:         ls.ProvinceName,
			CityName:             ls.CityName,
			DistrictName:         ls.DistrictName,
			Address:              ls.Address,
			CreateTime:           common.TimeToString(ls.CreateTime),
			Status:               ls.Status,
			Reason:               ls.Reason,
			LastLoginTime:        common.TimeToString(ls.LastLoginTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
