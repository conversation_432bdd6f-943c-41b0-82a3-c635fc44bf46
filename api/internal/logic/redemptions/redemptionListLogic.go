package redemptions

import (
	"context"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"github.com/Masterminds/squirrel"
	"golang.org/x/sync/errgroup"

	"engine/api/internal/svc"
	"engine/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RedemptionListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRedemptionListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RedemptionListLogic {
	return &RedemptionListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RedemptionListLogic) RedemptionList(req *types.RedemptionListReq) (resp *types.RedemptionListResp, err error) {
	var (
		wait  errgroup.Group
		total int64
		list  []model.MerchantRedemptionListInfo
	)

	// 构建查询条件
	where := squirrel.And{}
	// 添加搜索条件
	if req.Status != 0 {
		where = append(where, squirrel.Eq{"r.status": req.Status})
	}
	if req.StartTime != "" && req.EndTime != "" {
		where = append(where, squirrel.GtOrEq{"r.create_time": req.StartTime}, squirrel.LtOrEq{"r.create_time": req.EndTime})
	}
	if req.Keyword != "" {
		where = append(where, squirrel.Or{
			squirrel.Like{"m.company_name": "%" + req.Keyword + "%"},
			squirrel.Like{"r.goods_name": "%" + req.Keyword + "%"},
			squirrel.Like{"r.consignee": "%" + req.Keyword + "%"},
			squirrel.Like{"r.address": "%" + req.Keyword + "%"},
		})
	}

	// 获取总数
	wait.Go(func() error {
		builder := squirrel.Select("count(*) as ct").
			From(l.svcCtx.MerchantRedemptionModel.TableName() + " r").
			LeftJoin(l.svcCtx.MerchantModel.TableName() + " m on r.mid = m.id").
			Where(where)
		ct, er := l.svcCtx.MerchantRedemptionModel.FindCount(l.ctx, builder)
		if er != nil {
			l.Errorf("RedemptionList.MerchantRedemptionModel.FindCount err: %v", er)
			return er
		}
		total = ct
		return nil
	})

	// 查询数据
	wait.Go(func() error {
		builder := squirrel.Select("m.company_name,r.*").
			From(l.svcCtx.MerchantRedemptionModel.TableName() + " r").
			LeftJoin(l.svcCtx.MerchantModel.TableName() + " m on r.mid = m.id").
			Where(where).
			Offset(model.GetOffset(req.Page, req.Limit)).Limit(uint64(req.Limit))

		if req.OrderBy != "" && req.OrderColumn != "" && common.InSlice(req.OrderColumn, []string{"create_time"}) && common.InSlice(req.OrderBy, []string{"asc", "desc"}) {
			builder = builder.OrderBy(fmt.Sprintf("%s %s", req.OrderColumn, req.OrderColumn))
		} else {
			builder = builder.OrderBy("create_time DESC")
		}

		er := l.svcCtx.MerchantRedemptionModel.FindRows(l.ctx, builder, &list)
		if er != nil {
			l.Errorf("RedemptionList.MerchantRedemptionModel.FindRows err: %v", er)
			return er
		}
		return nil
	})

	err = wait.Wait()
	if err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	resp = new(types.RedemptionListResp)
	resp.Total = total
	resp.List = make([]types.RedemptionListInfo, 0, len(list))
	for _, ls := range list {
		info := types.RedemptionListInfo{
			Id:             ls.Id,
			CompanyName:    ls.CompanyName,
			ShortCode:      ls.ShortCode,
			GoodsName:      ls.GoodsName,
			RedeemQuantity: ls.RedeemQuantity,
			ProvinceName:   ls.ProvinceName,
			CityName:       ls.CityName,
			DistrictName:   ls.DistrictName,
			Address:        ls.Address,
			Consignee:      ls.Consignee,
			ContactPhone:   ls.ContactPhone,
			CreateTime:     common.TimeToString(ls.CreateTime),
			Status:         ls.Status,
			ShippingCode:   ls.ShippingCode,
			ShipTime:       common.TimeToString(ls.ShipTime),
		}
		resp.List = append(resp.List, info)
	}

	return resp, nil
}
