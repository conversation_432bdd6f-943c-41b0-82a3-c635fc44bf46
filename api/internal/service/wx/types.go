package wx

type JsCodeResponse struct {
	SessionKey string `json:"session_key"`
	OpenID     string `json:"openid"`
	UnionID    string `json:"unionid"`
	ErrCode    int    `json:"errcode,omitempty"`
	ErrMsg     string `json:"errmsg,omitempty"`
}

type UserInfo struct {
	OpenID    string `json:"openid"`
	Nickname  string `json:"nickname"`
	Telephone string `json:"telephone"`
	Avatar    string `json:"avatar"`
	UnionID   string `json:"unionid"`
}

type PhoneNumberResponse struct {
	ErrCode   int    `json:"errcode,omitempty"`
	ErrMsg    string `json:"errmsg,omitempty"`
	PhoneInfo struct {
		PhoneNumber     string `json:"phoneNumber"`
		PurePhoneNumber string `json:"purePhoneNumber"`
		CountryCode     string `json:"countryCode"`
	} `json:"phone_info"`
}

// DecryptedUserInfo 解密后的用户信息结构体
type DecryptedUserInfo struct {
	OpenID          string `json:"openId"`
	NickName        string `json:"nickName"`
	Gender          int    `json:"gender"`
	City            string `json:"city"`
	Province        string `json:"province"`
	Country         string `json:"country"`
	AvatarURL       string `json:"avatarUrl"`
	UnionID         string `json:"unionId,omitempty"`
	Language        string `json:"language"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	PhoneNumber     string `json:"phoneNumber"`
	Watermark       struct {
		AppID     string `json:"appid"`
		Timestamp int64  `json:"timestamp"`
	} `json:"watermark"`
}

// DecryptedPhoneInfo 解密后的手机号信息结构体
type DecryptedPhoneInfo struct {
	PhoneNumber     string `json:"phoneNumber"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	CountryCode     string `json:"countryCode"`
	Watermark       struct {
		AppID     string `json:"appid"`
		Timestamp int64  `json:"timestamp"`
	} `json:"watermark"`
}

// DecryptedResult 解密结果联合体
type DecryptedResult struct {
	UserInfo  *DecryptedUserInfo
	PhoneInfo *DecryptedPhoneInfo
}
