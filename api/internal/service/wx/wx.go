package wx

import (
	"encoding/json"
	"engine/api/internal/svc"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
)

type WxLogic struct {
	svcCtx *svc.ServiceContext
}

func NewWxLogic(svcCtx *svc.ServiceContext) *WxLogic {
	return &WxLogic{
		svcCtx: svcCtx,
	}
}

// GetOpenID 获取微信openid和session_key
func (l *WxLogic) GetOpenID(code string) (*JsCodeResponse, error) {
	const apiURL = "https://api.weixin.qq.com/sns/jscode2session"

	params := map[string]string{
		"appid":      l.svcCtx.AppletAppid,
		"secret":     l.svcCtx.AppletSecret,
		"js_code":    code,
		"grant_type": "authorization_code",
	}

	resp, err := l.svcCtx.HttpClient.Get(apiURL, params, nil)
	if err != nil {
		return nil, errors.Wrap(err, "failed to request wechat API")
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("wechat API returned status code: %d", resp.StatusCode())
	}

	var result JsCodeResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal wechat response")
	}

	// 检查微信返回的错误
	if result.ErrCode != 0 {
		return nil, fmt.Errorf("wechat API error: %d - %s", result.ErrCode, result.ErrMsg)
	}

	if result.SessionKey == "" || result.OpenID == "" {
		return nil, errors.New("invalid wechat response: missing session_key or openid")
	}

	return &result, nil
}

// GetUserInfo 获取微信用户信息
func (l *WxLogic) GetUserInfo(code, p_code string) (*UserInfo, error) {
	// 1. 参数校验
	if code == "" {
		return nil, errors.New("参数不完整")
	}

	// 2. 获取session_key和openid
	jsCodeResp, err := l.GetOpenID(code)
	if err != nil {
		return nil, errors.Wrap(err, "获取openid失败")
	}

	// 4. 获取手机号（可选）
	phoneNumber, err := l.GetUserPhoneNumber(p_code)
	if err != nil {
		return nil, errors.Wrap(err, "获取手机号失败")
	}

	// 5. 组装返回
	return &UserInfo{
		OpenID:    jsCodeResp.OpenID,
		UnionID:   jsCodeResp.UnionID,
		Telephone: phoneNumber,
	}, nil
}

// GetUserPhoneNumber 获取微信用户手机号
func (l *WxLogic) GetUserPhoneNumber(code string) (string, error) {
	// 1. 获取access token
	accessToken, err := l.getWeChatAppletAccessToken()
	if err != nil {
		return "", errors.Wrap(err, "failed to get access token")
	}

	// 2. 调用微信API获取手机号
	phoneNumber, err := l.callWeChatPhoneAPI(accessToken, code)
	if err != nil {
		return "", errors.Wrap(err, "failed to get phone number")
	}

	return phoneNumber, nil
}

// callWeChatPhoneAPI 调用微信手机号API
func (l *WxLogic) callWeChatPhoneAPI(accessToken, code string) (string, error) {
	url := fmt.Sprintf("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s", accessToken)

	// 构建请求体
	requestBody := map[string]string{
		"code": code,
	}

	// jsonBody, err := json.Marshal(requestBody)
	// if err != nil {
	// 	return "", errors.Wrap(err, "failed to marshal request body")
	// }

	// 设置请求头
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	// 发送POST请求
	resp, err := l.svcCtx.HttpClient.PostJson(url, requestBody, headers)
	if err != nil {
		return "", errors.Wrap(err, "failed to request phone number API")
	}

	// 解析响应
	var result PhoneNumberResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return "", errors.Wrap(err, "failed to unmarshal phone number response")
	}

	if result.ErrCode != 0 {
		return "", fmt.Errorf("wechat API error: %d - %s", result.ErrCode, result.ErrMsg)
	}

	if result.PhoneInfo.PurePhoneNumber == "" {
		return "", errors.New("empty phone number returned")
	}

	return result.PhoneInfo.PurePhoneNumber, nil
}

// getWeChatAppletAccessToken 获取微信小程序access token
func (l *WxLogic) getWeChatAppletAccessToken() (string, error) {

	// 构建请求URL
	path := "/wechat/v3/minapp/accesstoken"
	url := l.svcCtx.Config.ITEM.WECHART_URL + path

	// 构建请求参数
	params := map[string]string{
		"appid": l.svcCtx.AppletAppid,
	}

	// 发送GET请求
	resp, err := l.svcCtx.HttpClient.Get(url, params, nil)
	if err != nil {
		return "", errors.Wrap(err, "failed to request access token")
	}

	// 解析响应
	var result struct {
		AccessToken string `json:"access_token"`
		ErrCode     int    `json:"errcode,omitempty"`
		ErrMsg      string `json:"errmsg,omitempty"`
	}

	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return "", errors.Wrap(err, "failed to unmarshal access token response")
	}

	if result.ErrCode != 0 {
		return "", fmt.Errorf("wechat API error: %d - %s", result.ErrCode, result.ErrMsg)
	}

	if result.AccessToken == "" {
		return "", errors.New("empty access token returned")
	}

	return result.AccessToken, nil
}
