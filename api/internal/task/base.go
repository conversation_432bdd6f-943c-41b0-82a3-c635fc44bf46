package task

import "github.com/robfig/cron/v3"

// Task 是一个接口，定义了所有任务需要实现的接口
type Task interface {
	Execute() // 定义具体业务逻辑的执行
	Stop()    // 退出
}

type BaseTask struct {
}

// BaseManager 管理所有定时任务
type BaseManager struct {
	cron *cron.Cron
}

// NewTaskManager 创建一个新的任务管理器
func NewTaskManager() *BaseManager {
	return &BaseManager{
		cron: cron.New(cron.WithSeconds()), // 支持秒级精度
	}
}

// AddTask 添加一个任务到管理器
func (tm *BaseManager) AddTask(spec string, task Task) (cron.EntryID, error) {
	// 立即执行一次
	//task.Execute()

	// 添加定时任务
	return tm.cron.AddFunc(spec, func() {
		task.Execute()
	})
}

// Start 启动所有任务
func (tm *BaseManager) Start() {
	tm.cron.Start()
}

// Stop 停止所有任务
func (tm *BaseManager) Stop() {
	tm.cron.Stop()

	// 调用每个任务的Stop方法
	for _, entry := range tm.cron.Entries() {
		if task, ok := entry.Job.(interface{ Stop() }); ok {
			task.Stop()
		}
	}
}
