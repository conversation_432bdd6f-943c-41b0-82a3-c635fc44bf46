// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"
	"time"

	merchant "engine/api/internal/handler/merchant"
	prize "engine/api/internal/handler/prize"
	redemptions "engine/api/internal/handler/redemptions"
	user "engine/api/internal/handler/user"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/oneclicklogin",
					Handler: merchant.WeChatAppletPhoneLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/merchant/miniprogram"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.MerchantAuth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/binding",
					Handler: merchant.BindingHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/merchant/miniprogram"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/change",
					Handler: merchant.ChangeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/merchantList",
					Handler: merchant.MerchantListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/merchant/admin"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/batchAdd",
					Handler: prize.BatchAddHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/batchChange",
					Handler: prize.BatchChangeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/batchList",
					Handler: prize.BatchListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/prizeConfAdd",
					Handler: prize.PrizeConfAddHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/prizeConfDetail",
					Handler: prize.PrizeConfDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/prizeConfUpdare",
					Handler: prize.PrizeConfUpdareHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/prizeRecordList",
					Handler: prize.PrizeRecordListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/prize/admin"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/redemptionList",
					Handler: redemptions.RedemptionListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/redemptions/admin"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/oneclicklogin",
					Handler: user.WeChatAppletPhoneLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoRedeem/v1/user"),
	)
}
