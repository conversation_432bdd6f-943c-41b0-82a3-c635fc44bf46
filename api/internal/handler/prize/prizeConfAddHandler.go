package prize

import (
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"engine/api/internal/logic/prize"
	"engine/api/internal/svc"
	"engine/api/internal/types"
)

func PrizeConfAddHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.PrizeConfAddReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := prize.NewPrizeConfAddLogic(r.Context(), svcCtx)
		err := l.PrizeConfAdd(&req)
		result.HttpResult(r, w, result.<PERSON><PERSON><PERSON><PERSON>{}, err)
	}
}
