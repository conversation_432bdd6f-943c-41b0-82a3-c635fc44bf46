package merchant

import (
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"engine/api/internal/logic/merchant"
	"engine/api/internal/svc"
	"engine/api/internal/types"
)

func WeChatAppletPhoneLoginHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.WeChatAppletPhoneLoginReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := merchant.NewWeChatAppletPhoneLoginLogic(r.Context(), svcCtx)
		resp, err := l.WeChatAppletPhoneLogin(&req)
		result.HttpResult(r, w, resp, err)
	}
}
