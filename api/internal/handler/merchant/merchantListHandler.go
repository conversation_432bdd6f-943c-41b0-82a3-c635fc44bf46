package merchant

import (
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"engine/api/internal/logic/merchant"
	"engine/api/internal/svc"
	"engine/api/internal/types"
)

func MerchantListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.MerchantListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := merchant.NewMerchantListLogic(r.Context(), svcCtx)
		resp, err := l.MerchantList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
