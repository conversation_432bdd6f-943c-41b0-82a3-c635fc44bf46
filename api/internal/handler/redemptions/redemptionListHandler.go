package redemptions

import (
	"engine/common/result"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"engine/api/internal/logic/redemptions"
	"engine/api/internal/svc"
	"engine/api/internal/types"
)

func RedemptionListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RedemptionListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := redemptions.NewRedemptionListLogic(r.Context(), svcCtx)
		resp, err := l.RedemptionList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
