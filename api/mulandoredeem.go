package main

import (
	cf "engine/common/config"
	_ "engine/common/logger"
	"fmt"
	"net/http"

	"engine/api/internal/config"
	"engine/api/internal/handler"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func main() {

	c := config.NewConfig()
	cf.InitApiConfig(c, "go-mulandoredeem", "vinehoo.conf", 0)

	server := rest.MustNewServer(c.RestConf, rest.WithCustomCors(func(header http.Header) {
		header.Set("Access-Control-Allow-Headers", "Content-Type, Origin")
		header.Set("Access-Control-Allow-Origin", "*")
		header.Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	}, func(w http.ResponseWriter) {
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Stop()

	ctx := svc.NewServiceContext(*c)
	handler.RegisterHandlers(server, ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
