syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    //分页参数
    Paging {
        Page int64 `form:"page,default=1"`
        Limit int64 `form:"limit,default=10"`
    }

    IdJ {
        Id int64 `json:"id" validate:"required" v:"数据id"`
    }
    IdJU {
        Id uint64 `json:"id" validate:"required" v:"数据id"`
    }

    IdF {
        Id int64 `form:"id" validate:"required" v:"数据id"`
    }
    IdFU {
        Id uint64 `form:"id" validate:"required" v:"数据id"`
    }

    MiniPayResp {
        AppId string `json:"app_id"`
        TimeStamp string `json:"time_stamp"`
        NonceStr string `json:"nonce_str"`
        Package string `json:"package"`
        SignType string `json:"sign_type"`
        PaySign string `json:"pay_sign"`
    }
)
