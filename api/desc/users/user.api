syntax = "v1"

info(
    title: "用户"
    author: "gangh"
    email: "<EMAIL>"
    version: "v1"
)

type (
    WeChatAppletPhoneLoginReq {
        Code string `json:"code" validate:"required"`
        Pcode string `json:"p_code" validate:"required"`
    }
    WeChatAppletPhoneLoginResp {
        Token string `json:"token"`
    }
)

@server(
    middleware: Global
    group: user
    prefix: /mulandoRedeem/v1/user
)

service mulandoRedeem {
    @handler WeChatAppletPhoneLogin //微信小程序一键登录
    post /oneclicklogin (WeChatAppletPhoneLoginReq) returns (WeChatAppletPhoneLoginResp)
}